# Helper functions shared by both module-has.py and restore-dependencies.py

from hashlib import md5
from subprocess import check_output
from os.path import dirname, join
import sys

from yaml import safe_load_all
import yaml

# make use of the fact that this script is located in [root]/GitLab/ folder:
REPO_ROOT_DIR = dirname(dirname(__file__))
DEFAULT_KOTLIN_FILES_TO_CHECK = [
    "Kotlin/settings.gradle.kts",
    "Kotlin/buildSrc/build.gradle.kts",
    "Kotlin/buildSrc/src/main/kotlin/hero/kotlin-conventions.gradle.kts",
    "Kotlin/buildSrc/src/main/kotlin/hero/cloud-function-conventions.gradle.kts",
    "Kotlin/buildSrc/settings.gradle.kts",
    "Kotlin/versions.properties",
    ".gitlab-ci.yml",
    "Kotlin/.gitlab-ci.yml",
]
DEFAULT_NODE_FILES_TO_CHECK = ["NodeJs/.gitlab-ci.yml", "NodeJs/Run/.gitlab-ci.yml"]

def reference_constructor(loader, node):
    return node.value

def file_md5(fname):
    hash_md5 = md5()
    with open(fname, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def compute_module_hash(job_name, additional_folders=None):
    affecting_folders = _get_affecting_folders(job_name)
    default_files = DEFAULT_KOTLIN_FILES_TO_CHECK if "kotlin" in job_name.lower() else DEFAULT_NODE_FILES_TO_CHECK
    print(f'Using these default files {default_files}', file=sys.stderr)
    all_folders = sorted(set(affecting_folders + (additional_folders or [])))  # deduplicate and be order-independent
    hashes = [_get_folder_hash(folder) for folder in all_folders] + [bytes(file_md5(REPO_ROOT_DIR + "/" + additional_file), "UTF-8") for additional_file in default_files]
    return md5(b''.join(hashes + [job_name.encode()])).hexdigest()[:8]


def get_job_dependencies(job_name):
    yaml.SafeLoader.add_constructor('!reference', reference_constructor)
    """Get names of prerequisite jobs by looking up them in appropriate .gitlab-ci.yaml file(s)."""
    job_folder = _job_to_folder(job_name)
    # we can be run in any directory, hook into REPO_ROOT_DIR
    gitlab_yaml_path = join(REPO_ROOT_DIR, job_folder, '.gitlab-ci.yml')
    with open(gitlab_yaml_path) as stream:
        gitlab_yaml = next(safe_load_all(stream))
    job_def = gitlab_yaml[job_name]

    try:
        job_dep = job_def['needs']
        for job in job_dep:
            job_dep = list(set(job_dep + get_job_dependencies(job)))
        return job_dep
    except KeyError:
        pass  # try parent jobs. The dependencies value is a list, we don't need to do any merges

    parent_jobs = job_def.get('extends', [])
    # parent job can be just a string
    if isinstance(parent_jobs, str):
        parent_jobs = [parent_jobs]
    return [dep for parent_job in parent_jobs for dep in (get_job_dependencies(parent_job) or [])]


def _get_affecting_folders(job_name):
    dependencies = get_job_dependencies(job_name)
    print("Computing hash from following dependencies:", file=sys.stderr)
    print(*dependencies, file=sys.stderr)
    return [_job_to_folder(job) for job in dependencies + [job_name]]


def _get_folder_hash(folder):
    assert folder, folder
    # https://stackoverflow.com/a/19148946/4345715
    folder_hash = check_output(['git', 'rev-parse', f'HEAD:{folder}']).strip()
    return folder_hash


def _job_to_folder(job_name):
    pos = job_name.rfind('/')
    return job_name[:pos].lstrip('.') if pos > 0 else ''
