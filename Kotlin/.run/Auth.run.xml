<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Auth" type="JetRunConfigurationType">
    <option name="ALTERNATIVE_JRE_PATH" value="21" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <envs>
      <env name="SERVICE_NAME" value="auth" />
      <env name="SERVICE_TYPE" value="cloud_run" />
      <env name="SERVER_PORT" value="8080" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="hero.auth.MainKt" />
    <module name="Hero.Run.Auth.main" />
    <shortenClasspath name="NONE" />
    <option name="VM_PARAMETERS" value="--add-opens=java.base/java.time=ALL-UNNAMED" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="true" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        <ENTRY IS_ENABLED="true" PARSER="env" IS_EXECUTABLE="false" PATH=".env" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>