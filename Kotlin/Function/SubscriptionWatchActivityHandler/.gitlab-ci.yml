Kotlin/Function/SubscriptionWatchActivityHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/SubscriptionWatchActivityHandler/variables:
  variables:
    FUNCTION_NAME: "subscription-watch-activity-handler"
    CLASS_NAME: "hero.functions.SubscriptionWatchActivityHandler"
    TOPIC: "SubscriberStatusChanged"

Kotlin/Function/SubscriptionWatchActivityHandler/deploy-devel:
  needs:
    - Kotlin/Function/SubscriptionWatchActivityHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/SubscriptionWatchActivityHandler/variables

Kotlin/Function/SubscriptionWatchActivityHandler/deploy-prod:
  needs:
    - Kotlin/Function/SubscriptionWatchActivityHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/SubscriptionWatchActivityHandler/variables
