Kotlin/Function/GiftedSubscriptionExpirationNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/GiftedSubscriptionExpirationNotifier/variables:
  variables:
    FUNCTION_NAME: "gifted-subscription-expiration-notifier"
    CLASS_NAME: "hero.functions.GiftedSubscriptionExpirationNotifier"
    TOPIC: "Hourly"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/GiftedSubscriptionExpirationNotifier/deploy-devel:
  needs:
    - Kotlin/Function/GiftedSubscriptionExpirationNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/GiftedSubscriptionExpirationNotifier/variables

Kotlin/Function/GiftedSubscriptionExpirationNotifier/deploy-prod:
  needs:
    - Kotlin/Function/GiftedSubscriptionExpirationNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/GiftedSubscriptionExpirationNotifier/variables
  variables:
    TIMEOUT: "300s"
