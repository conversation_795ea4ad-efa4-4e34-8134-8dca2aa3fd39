Kotlin/Function/User/AirTableExport/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/User/AirTableExport/variables:
  variables:
    FUNCTION_NAME: "airtable-export"
    CLASS_NAME: "hero.functions.AirTableExport"
    TRIGGER: "$FIRESTORE_TRIGGER=prod-users/{userId}"
    # we need `write` access as the consumer stores timestamp of export
    SERVICE_ACCOUNT: "<EMAIL>"
    ENV_VARS: "AIRTABLE_TOKEN=$AIRTABLE_TOKEN"

# We omit devel & staging deployment to avoid clashing with production.
Kotlin/Function/User/AirTableExport/deploy-production:
  needs:
    - Kotlin/Function/User/AirTableExport/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/User/AirTableExport/variables
