Kotlin/Function/Post/DeletedPostHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/Gjirafa/build
    - Kotlin/Modules/SQL/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/DeletedPostHandler/variables:
  variables:
    FUNCTION_NAME: "deleted-post-handler"
    CLASS_NAME: "hero.functions.DeletedPostHandler"
    TOPIC: "PostStateChanged"
    # TODO limit service account to SQL + Firestore

Kotlin/Function/Post/DeletedPostHandler/deploy-devel:
  needs:
    - Kotlin/Function/Post/DeletedPostHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/DeletedPostHandler/variables

Kotlin/Function/Post/DeletedPostHandler/deploy-staging:
  needs:
    - Kotlin/Function/Post/DeletedPostHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Post/DeletedPostHandler/variables

Kotlin/Function/Post/DeletedPostHandler/deploy-prod:
  needs:
    - Kotlin/Function/Post/DeletedPostHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/DeletedPostHandler/variables
  variables:
    ENV_VARS: "FF_DELETE_LIVE_VIDEOS=disabled"
