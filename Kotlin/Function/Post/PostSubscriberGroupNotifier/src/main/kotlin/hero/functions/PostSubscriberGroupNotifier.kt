package hero.functions

import com.google.firebase.ErrorCode
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.preventGmailLinks
import hero.baseutils.publicFunctionCall
import hero.baseutils.systemEnvRelaxed
import hero.baseutils.truncate
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.jwt.toJwt
import hero.messaging.initFirebaseMessaging
import hero.messaging.sendMulticastMessage
import hero.model.Notification
import hero.model.NotificationType.NEW_LIVESTREAM
import hero.model.NotificationType.NEW_POST
import hero.model.Post
import hero.model.StorageEntityType
import hero.model.User
import hero.model.UserStatus
import hero.model.topics.EmailPublished
import hero.model.topics.PostNotificationGroupPrepared
import hero.repository.device.fetchFirebaseRegistrationTokens
import hero.repository.notification.NotificationRepository
import hero.repository.post.PostRepository
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import com.google.firebase.messaging.Notification as FirebaseNotification

@Suppress("unused")
class PostSubscriberGroupNotifier(
    private val hostname: String = SystemEnv.hostname,
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val firebaseMessaging: FirebaseMessaging = initFirebaseMessaging(
        SystemEnv.cloudProject,
        production.envPrefix,
    ),
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val postRepository: PostRepository = PostRepository(lazyContext),
    private val userRepository: UserRepository = UserRepository(lazyContext),
    private val notificationRepository: NotificationRepository = NotificationRepository(lazyContext),
) : PubSubSubscriber<PostNotificationGroupPrepared>() {
    private val context: DSLContext by lazyContext

    override fun consume(payload: PostNotificationGroupPrepared) {
        val creator = userRepository.findById(payload.creatorId)
        if (creator == null || creator.status != UserStatus.ACTIVE) {
            return
        }

        log.info(
            "Notifying group of ${payload.subscriberIds.size} subscribers for a new posts ${payload.postIds}.",
            mapOf("creatorId" to creator.id, "postIds" to payload.postIds),
        )
        val posts = postRepository.find {
            this
                .where(Tables.POST.ID.`in`(payload.postIds))
                .orderBy(Tables.POST.PUBLISHED_AT.asc())
        }

        val subscribers = userRepository.find { where(Tables.USER.ID.`in`(payload.subscriberIds)) }

        runBlocking {
            val notifications = subscribers
                .filter { subscriber -> subscriber.status == UserStatus.ACTIVE }
                .map { subscriber -> async { notify(creator, subscriber, posts) } }
                .awaitAll()
                .flatten()

            notificationRepository.saveAll(notifications)
        }
    }

    private fun notify(
        creator: User,
        subscriber: User,
        posts: List<Post>,
    ): List<Notification> {
        val postIds = posts.map { it.id }
        try {
            val postWithNotifications = posts.map {
                val notification = notifyInUi(creator, subscriber, it)
                notifyByEmail(creator, subscriber, it)
                it to notification
            }

            val (lastPost, lastNotification) = postWithNotifications.lastOrNull() ?: return emptyList()
            notifyByPush(creator, subscriber, lastPost, lastNotification)

            return postWithNotifications.map { it.second }
        } catch (e: Throwable) {
            log.fatal(
                "Cannot notify User ${subscriber.id} for Creator ${creator.id} and posts $postIds.",
                mapOf("creatorId" to creator.id, "userId" to subscriber.id, "postIds" to postIds),
                e,
            )

            return emptyList()
        }
    }

    private fun notifyByPush(
        creator: User,
        subscriber: User,
        post: Post,
        notification: Notification,
    ) {
        if (systemEnvRelaxed("FF_PUSH_NOTIFICATION") != "enabled") {
            log.info("Push notifications are disabled in ${SystemEnv.environment}")
            return
        }

        if (!subscriber.notificationsEnabled.pushNewPost) {
            return
        }

        val tokens = fetchFirebaseRegistrationTokens(context, subscriber.id)
        if (tokens.isEmpty()) {
            return
        }

        val message = buildMessage(creator, post, notification)
        sendMulticastMessage(firebaseMessaging, message, tokens, subscriber.id, log)
    }

    private fun buildMessage(
        creator: User,
        post: Post,
        notification: Notification,
    ): MulticastMessage.Builder =
        MulticastMessage.builder()
            .setNotification(
                FirebaseNotification.builder()
                    .setTitle(creator.name)
                    // We use FirebaseNotification text body only when the `post.text` is filled in.
                    // This does not allow the text to be localized, so for generic texts like
                    // "There is a new post", we use localized variant specific for Apple, see below.
                    .apply { if (post.text.isNotEmpty()) setBody(post.text.truncate(80)) }
                    .build(),
            )
            .putData("notification_type", if (post.isLivestream()) NEW_LIVESTREAM.name else NEW_POST.name)
            .putData("post_id", post.id)
            .putData("notification_id", notification.id)
            .apply {
                if (post.text.isEmpty()) {
                    val alert = ApsAlert.builder().setLocalizationKey(
                        if (post.isLivestream())
                            "push_notification_new_livestream_body"
                        else
                            "push_notification_new_post_body",
                    ).build()
                    // localised notification body specific to Apple, see above the FirebaseNotification title
                    val aps = Aps.builder().setAlert(alert).build()
                    val apns = ApnsConfig.builder().setAps(aps).build()
                    setApnsConfig(apns)
                }
            }

    private val ignoreCodes = setOf(ErrorCode.NOT_FOUND)
    private val retryCodes = setOf(ErrorCode.UNKNOWN, ErrorCode.INTERNAL, ErrorCode.UNAVAILABLE)

    private fun notifyByEmail(
        creator: User,
        subscriber: User,
        post: Post,
    ) {
        if (!subscriber.notificationsEnabled.emailNewPost || subscriber.email.isNullOrBlank()) {
            // user cannot be notified by email
            return
        }

        val jwt = mapOf("userId" to subscriber.id, "notificationType" to NEW_POST).toJwt()
        val thumbnail = post.assets.firstNotNullOfOrNull { it.thumbnail ?: it.image?.id }

        pubSub.publish(
            EmailPublished(
                from = creator.name,
                to = subscriber.email!!,
                // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                template = "new-posts",
                variables = listOf(
                    "post-link" to "$hostname/${creator.path}/post/${post.id}",
                    "user-name" to subscriber.name,
                    "creator-name" to creator.name,
                    "preview" to post.text.truncate(300).preventGmailLinks(),
                    "thumbnail" to thumbnail,
                    "is-livestream" to post.isLivestream(),
                    "is-livestream-live" to post.isLivestreamLive(),
                    "unsubscribe-link" to publicFunctionCall("notification-disabler") + "?request=$jwt",
                ),
                language = subscriber.language,
            ),
        )
    }

    private fun notifyInUi(
        creator: User,
        subscriber: User,
        post: Post,
    ): Notification =
        Notification(
            userId = subscriber.id,
            type = if (post.isLivestream()) NEW_LIVESTREAM else NEW_POST,
            actorIds = mutableListOf(creator.id),
            objectType = StorageEntityType.POST,
            objectId = post.id,
            created = post.published,
            timestamp = post.published,
        )
}
