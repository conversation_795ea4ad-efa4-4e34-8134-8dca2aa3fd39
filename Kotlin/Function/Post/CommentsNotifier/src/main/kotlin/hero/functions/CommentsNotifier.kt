package hero.functions

import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.preventGmailLinks
import hero.baseutils.systemEnvRelaxed
import hero.baseutils.truncate
import hero.core.logging.Logger
import hero.messaging.initFirebaseMessaging
import hero.messaging.sendMulticastMessage
import hero.model.Notification
import hero.model.NotificationType.NEW_COMMENT
import hero.model.NotificationType.NEW_REPLY
import hero.model.NotificationType.NEW_REPLY_TO_REPLY
import hero.model.Post
import hero.model.StorageEntityType
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.device.fetchFirebaseRegistrationTokens
import hero.repository.notification.NotificationRepository
import hero.repository.post.PostRepository
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import com.google.firebase.messaging.Notification as FirebaseNotification

@Suppress("unused")
class CommentsNotifier(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val firebaseMessaging: FirebaseMessaging = initFirebaseMessaging(
        SystemEnv.cloudProject,
        SystemEnv.isProduction.envPrefix,
    ),
    private val postRepository: PostRepository = PostRepository(lazyContext),
    private val notificationRepository: NotificationRepository = NotificationRepository(lazyContext),
    private val userRepository: UserRepository = UserRepository(lazyContext),
    private val logger: Logger = log,
    private val systemEnv: EnvironmentVariables = SystemEnv,
    private val clock: Clock = Clock.systemUTC(),
) : PubSubSubscriber<PostStateChanged>(systemEnv) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: PostStateChanged) =
        when {
            // we don't process posts here
            payload.post.parentId == null -> {}
            // published comment
            payload.stateChange == PostStateChange.PUBLISHED -> consumeNewComment(payload)
            // otherwise this is some edge case
            else -> {}
        }

    private fun consumeNewComment(request: PostStateChanged) {
        val comment = request.post
        logger.info("Notifying related commenters of comment ${comment.parentId}.", mapOf("userId" to comment.userId))

        val postIds = setOfNotNull(comment.parentId, comment.siblingId)
        val posts = postRepository.find {
            this
                .where(Tables.POST.ID.`in`(postIds))
                .and(Tables.POST.STATE.eq(PostState.PUBLISHED.name))
        }
        assert(posts.size <= 2) { "Found ${posts.size} for post ids $postIds" }

        // Use case:
        // 1. User A comments on a post.
        // 2. User A then replies to their own initial comment.
        // 3. Another user replies to this reply.
        //
        // Expected behavior:
        // - A notification is created only for the reply, not for the initial comment.
        if (posts.size == 2 && posts[0].userId == posts[1].userId) {
            notify(posts.first { it.id == comment.siblingId }, comment)
        } else {
            posts.forEach { notify(it, comment) }
        }
    }

    private fun notify(
        commentedPost: Post,
        comment: Post,
    ) {
        if (commentedPost.isLivestreamLive()) {
            return
        }

        if (commentedPost.userId == comment.userId) {
            return
        }

        val type = when {
            commentedPost.parentId == null -> NEW_COMMENT
            comment.siblingId == commentedPost.id -> NEW_REPLY_TO_REPLY
            else -> NEW_REPLY
        }
        val notification = Notification(
            userId = commentedPost.userId,
            type = type,
            actorIds = mutableListOf(comment.userId),
            objectType = StorageEntityType.POST,
            objectId = comment.id,
            created = Instant.now(clock),
            timestamp = Instant.now(clock),
        )

        logger.info(
            "Notifying with new comment ${notification.objectId}.",
            mapOf("userId" to notification.userId),
        )
        notificationRepository.save(notification)
        pushNotification(notification, comment)
    }

    private fun pushNotification(
        notification: Notification,
        comment: Post,
    ) {
        if (systemEnvRelaxed("FF_PUSH_NOTIFICATION") != "enabled") {
            logger.info("Push notifications are disabled in ${systemEnv.environment}")
            return
        }

        val commenter = userRepository.getById(comment.userId)
        val user = userRepository.getById(notification.userId)
        if (!user.notificationsEnabled.pushNewComment) {
            return
        }
        val tokens = fetchFirebaseRegistrationTokens(context, notification.userId)

        if (tokens.isEmpty()) {
            return
        }

        val text = comment.text.truncate(80).preventGmailLinks()

        val message = MulticastMessage.builder()
            .putData("notification_type", notification.type.name)
            .putData("comment_id", comment.id)
            .putData("post_id", comment.parentPostId)
            .putData("notification_id", notification.id)
            .setNotification(
                FirebaseNotification.builder()
                    .setTitle(commenter.name)
                    .build(),
            )
            .apply {
                val alert = ApsAlert
                    .builder()
                    .let {
                        val localizationKey = when {
                            notification.type == NEW_COMMENT && comment.text.isBlank() ->
                                "push_notification_new_asset_comment_body"

                            notification.type == NEW_COMMENT ->
                                "push_notification_new_text_comment_body"

                            // notification type is either reply or reply_to_reply now
                            comment.text.isBlank() ->
                                "push_notification_new_asset_comment_reply_body"

                            else ->
                                "push_notification_new_text_comment_reply_body"
                        }
                        if (comment.text.isBlank()) {
                            it.setLocalizationKey(localizationKey)
                        } else {
                            it
                                .setLocalizationKey(localizationKey)
                                .addLocalizationArg("\"$text\"")
                        }
                    }
                    .build()
                val aps = Aps.builder().setAlert(alert).build()
                val apns = ApnsConfig.builder().setAps(aps).build()
                setApnsConfig(apns)
            }

        sendMulticastMessage(firebaseMessaging, message, tokens, notification.userId, log)
    }
}
