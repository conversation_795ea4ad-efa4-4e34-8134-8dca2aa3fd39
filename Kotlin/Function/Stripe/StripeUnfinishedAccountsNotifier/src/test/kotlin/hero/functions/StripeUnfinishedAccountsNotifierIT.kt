package hero.functions

import hero.baseutils.minusDays
import hero.baseutils.truncated
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections.usersCollection
import hero.test.TestEnvironmentVariables
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class StripeUnfinishedAccountsNotifierIT : IntegrationTest() {
    private val now = Instant.now().truncated()
    private val underTest = StripeUnfinishedAccountsNotifier(
        production = false,
        firestore = firestore,
        pubSub = pubSubMock,
        usersCollection = usersCollection,
        systemEnvs = TestEnvironmentVariables,
    )

    @Test
    fun `ignore old accounts without notifications timestamps`() {
        testHelper.createUser(
            stripeAccountId = "stripe-account-id",
            stripeAccountCreatedAt = null,
            stripeAccountSuggestionSentAt = null,
            stripeAccountActive = false,
        )
        assertThat(underTest.fetchUsersToNotify()).isEmpty()
    }

    @Test
    fun `skip accounts which were already notified`() {
        testHelper.createUser(
            stripeAccountId = "stripe-account-id",
            stripeAccountCreatedAt = now.minusDays(5),
            stripeAccountSuggestionSentAt = Instant.now(),
            stripeAccountActive = false,
        )
        assertThat(underTest.fetchUsersToNotify()).isEmpty()
    }

    @Test
    fun `skip accounts which are too fresh`() {
        testHelper.createUser(
            stripeAccountId = "stripe-account-id",
            stripeAccountCreatedAt = now.minusDays(1),
            stripeAccountSuggestionSentAt = Instant.now(),
            stripeAccountActive = false,
        )
        assertThat(underTest.fetchUsersToNotify()).isEmpty()
    }

    @Test
    fun `skip accounts which are verified`() {
        testHelper.createUser(
            stripeAccountId = "stripe-account-id",
            stripeAccountCreatedAt = now.minusDays(5),
            stripeAccountSuggestionSentAt = null,
            stripeAccountActive = true,
        )
        assertThat(underTest.fetchUsersToNotify()).isEmpty()
    }

    @Test
    fun `notify accounts which are due`() {
        val user = testHelper.createUser(
            stripeAccountId = "stripe-account-id",
            stripeAccountCreatedAt = now.minusDays(5),
            stripeAccountSuggestionSentAt = null,
            stripeAccountActive = false,
        )

        assertThat(underTest.fetchUsersToNotify()).isEqualTo(listOf(user))
    }
}
