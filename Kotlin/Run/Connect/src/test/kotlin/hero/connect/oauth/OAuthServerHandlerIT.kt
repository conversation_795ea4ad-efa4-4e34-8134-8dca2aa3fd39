package hero.connect.oauth

import hero.http4k.extensions.CustomJackson.asA
import hero.jwt.authorization
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.time.TestClock
import io.jsonwebtoken.security.Keys
import org.assertj.core.api.Assertions.assertThat
import org.http4k.contract.contract
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.http4k.core.cookie.Cookie
import org.http4k.core.cookie.cookie
import org.http4k.core.cookie.cookies
import org.http4k.format.Jackson
import org.http4k.security.AccessTokenResponse
import org.junit.jupiter.api.Test
import java.security.SecureRandom
import java.time.Instant
import java.util.Random
import java.util.UUID

class OAuthServerHandlerIT : IntegrationTest() {
    @Test
    fun `should successfully start authorization`() {
        val now = Instant.ofEpochSecond(2373978593)
        val testClock = TestClock(now)
        val requestValidator = OAuthClientValidator(lazyTestContext)
        val authRequestTracking = OAuthRequestTracker(secretKey, testClock)
        val authorizationCodes = OAuthAuthorizationCodeService(lazyTestContext, SecureRandom())
        val accessTokenRequestAuthentication = OAuthClientCredentialsValidator(lazyTestContext)
        val accessTokens = OAuthAccessTokenGenerator(lazyTestContext, SecureRandom(), secretKey)
        val oAuthServerHandler = contract {
            this.routes += authorizationServer(
                requestValidator,
                authRequestTracking,
                authorizationCodes,
                accessTokenRequestAuthentication,
                accessTokens,
                "https://herohero.co/oauth-login",
            )
        }

        testHelper.createOAuthClient(
            id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
            redirectUris = listOf("https://redirect.url"),
        )

        val request = Request(Method.GET, "/authorize")
            .query("response_type", "code")
            .query("client_id", "042842bf-ecb8-44d3-ba61-c400c7d7e2fe")
            .query("scope", "subscription.read")
            .query("redirect_uri", "https://redirect.url")

        val response = oAuthServerHandler(request)

        assertThat(response.status).isEqualTo(Status.FOUND)
        assertThat(response.header("Location")).isEqualTo("https://herohero.co/oauth-login")

        val authTrackingCookie = response.cookies().first { it.name == "OauthFlowId" }
        assertThat(authTrackingCookie.value).isEqualTo(
            """
            eyJhbGciOiJIUzUxMiJ9.eyJjbGllbnRfaWQiOiIwNDI4NDJiZi1lY2I4LTQ0ZDMtYmE2MS1jNDAwYzdkN2UyZmUiLCJyZWRpcmVjdF91cmkiOiJodHRwczovL3JlZGlyZWN0LnVybCIsInNjb3BlcyI6WyJzdWJzY3JpcHRpb24ucmVhZCJdLCJyZXNwb25zZV90eXBlIjoiQ29kZSIsImV4cCI6MjM3Mzk3ODg5MywiaWF0IjoyMzczOTc4NTkzfQ.xmh4xTanUR6fHLbsUwwE14rYBkpI2tdu_DXMPl2LNOIjbV4q0vf0YsMD7VMUhxUMNJn3Jn3QLazlIu1UhVZkEw
            """.trimIndent(),
        )
        assertThat(authTrackingCookie.httpOnly).isTrue()
        assertThat(authTrackingCookie.secure).isTrue()
        assertThat(authTrackingCookie.expires).isNotNull()
    }

    @Test
    fun `should successfully complete authorization and receive authorization code`() {
        val now = Instant.ofEpochSecond(1742816068)
        val testClock = TestClock(now)
        val requestValidator = OAuthClientValidator(lazyTestContext)
        val authRequestTracking = OAuthRequestTracker(secretKey)

        val testRandom = Random(0)
        val authorizationCodes = OAuthAuthorizationCodeService(lazyTestContext, testRandom, testClock)
        val accessTokenRequestAuthentication = OAuthClientCredentialsValidator(lazyTestContext)
        val accessTokens = OAuthAccessTokenGenerator(lazyTestContext, SecureRandom(), secretKey)
        val oAuthServerHandler = contract {
            this.routes += authorizationServer(
                requestValidator,
                authRequestTracking,
                authorizationCodes,
                accessTokenRequestAuthentication,
                accessTokens,
                "",
            )
        }

        testHelper.createOAuthClient(
            id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
            redirectUris = listOf("https://redirect.url"),
        )
        testHelper.createUser("pepa")

        val request = Request(Method.POST, "/authorize")
            .cookie(
                Cookie(
                    "OauthFlowId",
                    // this is the same token we used above
                    value = """
                    eyJhbGciOiJIUzUxMiJ9.eyJjbGllbnRfaWQiOiIwNDI4NDJiZi1lY2I4LTQ0ZDMtYmE2MS1jNDAwYzdkN2UyZmUiLCJyZWRpcmVjdF91cmkiOiJodHRwczovL3JlZGlyZWN0LnVybCIsInNjb3BlcyI6WyJzdWJzY3JpcHRpb24ucmVhZCJdLCJyZXNwb25zZV90eXBlIjoiQ29kZSIsImV4cCI6MjM3Mzk3ODg5MywiaWF0IjoyMzczOTc4NTkzfQ.xmh4xTanUR6fHLbsUwwE14rYBkpI2tdu_DXMPl2LNOIjbV4q0vf0YsMD7VMUhxUMNJn3Jn3QLazlIu1UhVZkEw
                    """.trimIndent(),
                ),
            )
            .cookie(
                Cookie(
                    "accessToken2",
                    value = "pepa".authorization(),
                ),
            )
            .header("Content-Type", "application/x-www-form-urlencoded")

        val response = oAuthServerHandler(request)

        assertThat(response.status).isEqualTo(Status.SEE_OTHER)
        // client should be redirected back to the url with authorization code
        assertThat(response.header("Location"))
            .isEqualTo("https://redirect.url?code=YLQguzhR2dR6y5M9vnA5m_bJLaM68B1Pt3DpjAMl9B0")

        val authorizationCode = testContext
            .selectFrom(Tables.OAUTH_AUTHORIZATION_CODE)
            .fetchSingle()

        assertThat(authorizationCode.id).isEqualTo("YLQguzhR2dR6y5M9vnA5m_bJLaM68B1Pt3DpjAMl9B0")
        assertThat(authorizationCode.clientId).isEqualTo(UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"))
        assertThat(authorizationCode.userId).isEqualTo("pepa")
        assertThat(authorizationCode.redirectUri).isEqualTo("https://redirect.url")
        assertThat(authorizationCode.state).isNull()
        assertThat(authorizationCode.scopes).isEqualTo(arrayOf("subscription.read"))
        assertThat(authorizationCode.responseType).isEqualTo("Code")
        assertThat(authorizationCode.responseMode).isNull()
        assertThat(authorizationCode.codeChallenge).isNull()
        assertThat(authorizationCode.createdAt).isEqualTo(now)
        assertThat(authorizationCode.updatedAt).isEqualTo(now)
        assertThat(authorizationCode.usedAt).isNull()
    }

    @Test
    fun `should successfully exchange authorization code for access token`() {
        val now = Instant.ofEpochSecond(1742910188)
        val testClock = TestClock(now)
        val requestValidator = OAuthClientValidator(lazyTestContext)
        val authRequestTracking = OAuthRequestTracker(secretKey)

        val authorizationCodes = OAuthAuthorizationCodeService(lazyTestContext, Random(0), testClock)
        val accessTokenRequestAuthentication = OAuthClientCredentialsValidator(lazyTestContext)
        val accessTokens = OAuthAccessTokenGenerator(lazyTestContext, Random(0), secretKey, testClock)
        val oAuthServerHandler = contract {
            this.routes += authorizationServer(
                requestValidator,
                authRequestTracking,
                authorizationCodes,
                accessTokenRequestAuthentication,
                accessTokens,
                "",
                testClock,
            )
        }

        testHelper.createUser("cestmir")
        testHelper.createOAuthClient(
            id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
            userId = "cestmir",
            redirectUris = listOf("https://redirect.url"),
            secret = "SECRET",
        )
        testHelper.createUser("honza")
        testHelper.createOAuthAuthorizationCode(
            oauthClientId = "042842bf-ecb8-44d3-ba61-c400c7d7e2fe",
            userId = "honza",
            id = "ABCDF",
            createdAt = now,
            redirectUri = "https://redirect.url",
        )

        val request = Request(Method.POST, "/oauth2/token")
            .header("Content-Type", "application/x-www-form-urlencoded")
            .body(
                """
                grant_type=authorization_code&code=ABCDF&client_id=042842bf-ecb8-44d3-ba61-c400c7d7e2fe&client_secret=SECRET&redirect_uri=https://redirect.url
                """.trimIndent(),
            )

        val response = oAuthServerHandler(request)

        assertThat(response.status).isEqualTo(Status.OK)
        val returnedAccessToken = Jackson.parse(response.bodyString()).asA<AccessTokenResponse>()

        val expectedAccessToken = AccessTokenResponse(
            // {
            //  "sub": "honza",
            //  "exp": 1745502188,
            //  "iat": 1742910188,
            //  "type": "ACCESS",
            //  "scopes": [],
            //  "creatorId": "cestmir",
            //  "clientId": "042842bf-ecb8-44d3-ba61-c400c7d7e2fe",
            //  "iss": "herohero"
            // }
            access_token =
                """
                eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJob256YSIsImV4cCI6MTc0NTUwMjE4OCwiaWF0IjoxNzQyOTEwMTg4LCJ0eXBlIjoiQUNDRVNTIiwic2NvcGVzIjpbXSwiY3JlYXRvcklkIjoiY2VzdG1pciIsImNsaWVudElkIjoiMDQyODQyYmYtZWNiOC00NGQzLWJhNjEtYzQwMGM3ZDdlMmZlIiwiaXNzIjoiaGVyb2hlcm8ifQ.5we9eYQfe-pWAM5E088rs-Mj8f-yeZFjvxDerzqz7R6kzYwZTfZMrFRIuRGZakwf_3X60GZAjf2ButFscBuY3w
                """.trimIndent(),
            token_type = "Bearer",
            expires_in = 2592000,
            refresh_token = "YLQguzhR2dR6y5M9vnA5m_bJLaM68B1Pt3DpjAMl9B0",
        )

        assertThat(returnedAccessToken).isEqualTo(expectedAccessToken)
    }
}

private val secretKey = "5d192ef295f94e738479ab9f1952cd1bc1ae879697a344c9a62aabe73d0ddb47"
    .encodeToByteArray()
    .let {
        Keys.hmacShaKeyFor(it)
    }
