package hero.connect.oauth

import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.OauthClientRecord
import org.jooq.DSLContext
import java.util.Base64
import java.util.Random
import java.util.UUID

fun DSLContext.getClient(clientId: String): OauthClientRecord? {
    return try {
        UUID.fromString(clientId)
    } catch (e: Exception) {
        return null
    }?.let {
        this
            .selectFrom(Tables.OAUTH_CLIENT)
            .where(Tables.OAUTH_CLIENT.ID.eq(it))
            .and(Tables.OAUTH_CLIENT.DELETED_AT.isNull)
            .and(Tables.OAUTH_CLIENT.DISABLED_AT.isNull)
            .fetchOne()
    }
}

fun generateRandomId(
    random: Random,
    length: Int = 32,
): String {
    val bytes = ByteArray(length)
    random.nextBytes(bytes)
    return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
}
