package hero.media.controller.dto

import com.fasterxml.jackson.annotation.JsonAlias
import hero.gjirafa.dto.HealthStatus
import hero.gjirafa.dto.LiveVideoStatus
import java.time.Instant

data class GjirafaProcessingUrl(
    val url: String,
)

data class GjirafaAssetId(
    val id: String,
)

data class InternalGjirafaResponse<T>(val result: T)

data class GjirafaEvent<T>(
    val eventTime: String,
    val eventType: String,
    val data: T,
    val projectId: String,
)

data class GjirafaEventUploadsData(
    @get:JsonAlias("publicId")
    val id: String?,
)

data class GjirafaSubtitleAutogeneratedCompletedData(
    val videoId: String,
    val filePath: String,
)

data class GjirafaLivestreamEventData(
    val id: String?,
    val publicId: String?,
    val status: LiveVideoStatus?,
    val healthStatus: HealthStatus?,
    val playbackUrl: String?,
    val activeChannelId: String?,
    val liveStartDateUTC: Instant?,
    // TODO switch to Instant once following is resolved:
    // https://herohero-workspace.slack.com/archives/C04UCDB3CJK/p1737454763681699
    val liveCutStartDate: String?,
    val liveCutEndDate: String?,
)

data class GjirafaOriginalUploadResponse(
    val url: String,
)
