package hero.stripe.fees.controller

import hero.baseutils.instantOf
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class ReportWebhookControllerTest {
    @Test
    fun `generate sequence for interval`() {
        val underTest = ReportWebhookController(mockk(), mockk(), mockk())
        val sequences = underTest.generateSequencesInInterval(
            start = instantOf("2025-05-10T00:00:00Z"),
            end = instantOf("2025-05-10T00:20:00Z"),
            minutes = 5,
        )
        assertEquals(
            listOf(
                Pair(instantOf("2025-05-10T00:00:00Z"), instantOf("2025-05-10T00:05:00Z")),
                Pair(instantOf("2025-05-10T00:05:00Z"), instantOf("2025-05-10T00:10:00Z")),
                Pair(instantOf("2025-05-10T00:10:00Z"), instantOf("2025-05-10T00:15:00Z")),
                Pair(instantOf("2025-05-10T00:15:00Z"), instantOf("2025-05-10T00:20:00Z")),
            ),
            sequences,
        )
    }
}
