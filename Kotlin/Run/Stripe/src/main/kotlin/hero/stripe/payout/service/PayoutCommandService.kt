package hero.stripe.payout.service

import com.stripe.model.BalanceTransaction
import com.stripe.model.Charge
import com.stripe.model.Payout
import com.stripe.model.Refund
import hero.baseutils.format
import hero.baseutils.instantOf
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.baseutils.nullIfEmpty
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.root
import hero.gcloud.where
import hero.model.CZ_VAT_COUNTRY
import hero.model.Creator
import hero.model.Currency
import hero.model.Invoice
import hero.model.InvoiceItem
import hero.model.InvoiceItemType
import hero.model.PaymentType
import hero.model.SupportCounts
import hero.model.Tier
import hero.model.User
import hero.model.euCountries
import hero.model.restoreEmail
import hero.model.topics.EmailPublished
import hero.stripe.service.StripeService
import hero.stripe.service.VatMapping
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.ZoneOffset
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.random.Random

class PayoutCommandService(
    private val hostnameServices: String,
    private val pubSub: PubSub,
    private val stripe: StripeService,
    private val usersCollection: TypedCollectionReference<User>,
    private val tiersCollection: TypedCollectionReference<Tier>,
    private val invoicesCollection: TypedCollectionReference<Invoice>,
    private val countryToVatMapping: VatMapping,
) {
    private val heroheroVatPayerSince: Instant = instantOf("2023-04-01T00:00:00Z")

    /**
     * Public method which will generate a new invoice for given Stripe Payout. The result
     * is an invoice for our fee and a receipt for creator's income.
     * Invoicing & VAT specification: https://linear.app/herohero/issue/HH-638/invoicing-spec
     */
    fun execute(command: ProcessPayout): Invoice? {
        if (!command.overwriteInvoice) {
            getInvoiceForPayout(command.payoutId)?.let { return it }
        }
        // read the payout from stripe
        val payout = stripe.getPayout(command.accountId, command.payoutId, command.currency)
        // fetch invoicing details for Herohero company
        val creator = findByConnectedAccountId(command.accountId)
        if (creator == null) {
            // we have to die silently to prevent infinite Stripe webhook retries
            log.warn("Stripe webhooks us with ${command.accountId} for which User cannot be found.")
            return null
        }
        val creatorsCountry = creator.company?.country?.nullIfEmpty()?.uppercase() ?: CZ_VAT_COUNTRY
        val creatorsVatId = creator.company?.vatId.nullIfEmpty()
        // expected creator's Tier used when individual supporter's purchase cannot be obtained
        val creatorsTier = tiersCollection[creator.creator.tierId].get()
        // extract payout items from given payout
        val payoutItems = processPayout(
            command.accountId,
            payout,
            creatorsTier,
            creatorsCountry,
            command.currency,
            creatorsVatId,
            command.skipOtherAdjustments,
        )
        if (payoutItems.list.isEmpty()) {
            return null
        }
        // assert only single currency is paid out and store it
        val currencyInvoice = assertSingleCurrency(command.payoutId, command.accountId, payoutItems)
        // generate items to appear on the invoice
        val invoiceItems = generateInvoiceItems(payout, payoutItems, creatorsCountry, creatorsVatId)
        val payoutCreatedAt = Instant.ofEpochSecond(payout.created)
        val payoutDayAt = payoutCreatedAt.atZone(ZoneOffset.UTC).dayOfMonth

        // in case the payout was done a bit late, we have to shift it to the last day of previous month
        val invoiceCreatedAt = if (payoutDayAt < 5) {
            payoutCreatedAt.minusDays(payoutDayAt.toLong())
        } else {
            payoutCreatedAt
        }

        // and generate the invoice
        val invoice = generateInvoice(
            createdAt = invoiceCreatedAt,
            invoiceItems = invoiceItems,
            user = creator,
            countryOfDestination = creatorsCountry,
            accountId = command.accountId,
            payout = payout,
            currencyInvoice = currencyInvoice,
            // https://linear.app/herohero/issue/HH-638/invoicing-spec
            euReverseCharged = (creatorsCountry in euCountries && creatorsCountry != CZ_VAT_COUNTRY) &&
                creator.company?.vatId.nullIfEmpty() != null,
        )
        // then write the invoice to invoices collection and send by email
        storeInvoice(invoice, creator, command.overwriteInvoice, command.sendEmail)
        return invoice
    }

    internal fun processPayout(
        accountId: String,
        payout: Payout,
        creatorsTier: Tier,
        country: String,
        currency: Currency,
        creatorsVatId: String?,
        skipOtherAdjustment: Boolean,
    ): PayoutItems =
        if (payout.automatic)
            processAutomaticPayout(
                accountId,
                payout,
                creatorsTier,
                country,
                currency,
                creatorsVatId,
                skipOtherAdjustment,
            )
        else
            processManualPayout(payout, creatorsTier, country, creatorsVatId)

    /**
     * When Stripe pays out creator automatically in the beginning of month, we can mostly link all the supporter's
     * transactions to extract the original charges in original currency and relation to Tier. This is true up to
     * Stripe's `other_adjustment`, which happens with manual changes by Stripe support team (e.g. currency conversion).
     * The result of this method is a list of transactions which are being paid out, grouped by Tier and direction of the charge.
     */
    private fun processAutomaticPayout(
        accountId: String,
        payout: Payout,
        creatorsTier: Tier,
        country: String,
        currency: Currency,
        creatorsVatId: String?,
        skipOtherAdjustment: Boolean,
    ): PayoutItems {
        // When handling automatic payouts, we get the list of concerned transactions
        // which allow us to nicely generate invoices for each subscription charge.
        // This is not the case for manual payouts, see below.
        val chargesRaw = stripe.listPayoutTransactions(accountId, payout.id, currency)

        // https://linear.app/herohero/issue/HH-1359/stripe-messed-transfers
        val advances = chargesRaw
            .filter { it.reportingCategory in setOf("advance", "advance_funding") }

        if (advances.sumOf { it.amount } != 0L) {
            error("Advances did not sum to zero in: $accountId/${payout.id}")
        }

        if (chargesRaw.filter { it.reportingCategory == "payout" }.size > 1) {
            // in this case there may be correction for manual payout (see `it.sourceTransaction` and HH-1507)
            error("There should always be only single Payout transaction for $accountId/${payout.id}")
        }

        // Filtering unwanted balance transactions:
        // - payout - transaction which is the actual money transfer
        // - payout_reversal - transaction which is taking money back to the account from the creator
        // - advance, advance_funding - see above
        // - application fee refunds - https://linear.app/herohero/issue/HH-4122
        // related issues:
        // - https://gitlab.com/heroheroco/general/-/issues/525
        // - https://gitlab.com/heroheroco/general/-/issues/1461
        val charges = chargesRaw
            .filter { it.reportingCategory !in setOf("payout", "payout_reversal", "advance", "advance_funding") }
            // https://linear.app/herohero/issue/HH-4122
            .filter { !(it.reportingCategory == "fee" && "Application fee refund for" in it.description) }
            .filter { !skipOtherAdjustment || it.reportingCategory != "other_adjustment" }

        // https://linear.app/herohero/issue/HH-3217/attach-payout-id-to-transactions-when-paid-out
        val executor = Executors.newFixedThreadPool(10)
        charges.onEach {
            executor.submit {
                stripe.markTransactionAsPaidOut(
                    transaction = it,
                    accountId = accountId,
                    currency = currency,
                    payoutId = payout.id,
                )
            }
        }

        val payoutItems = charges
            .parallelStream()
            .map {
                try {
                    processBalanceTransaction(
                        it = it,
                        accountId = accountId,
                        payoutId = payout.id,
                        creatorsTier = creatorsTier,
                        creatorsVatId = creatorsVatId,
                        country = country,
                        currency = currency,
                    )
                } catch (e: Exception) {
                    throw IllegalStateException(
                        "Cannot process transaction ${it.id} with source ${it.source} of $accountId/${payout.id}.",
                        e,
                    )
                }
            }
            .toList()

        // we need to group by Tier and (charge/refund)
        val payoutGroups = payoutItems
            .groupingBy { it }.eachCount()
            .toList()
            .sortedBy {
                when (it.first.type) {
                    PaymentType.SUBSCRIPTION -> "a"
                    PaymentType.POST_UNLOCK -> "b"
                    PaymentType.OTHER_TRANSACTION -> "c"
                    PaymentType.COUPON -> "d"
                } +
                    "%30d".format(it.first.amountCents.toInt()) +
                    if (!it.first.isRefund) "a" else "b"
            }
            .map { PayoutGroupCounted(it.first, it.second) }

        // and wait until all transactions are marked with related payout
        executor.shutdown()
        executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)

        return PayoutItems(
            list = payoutGroups,
            chargesCount = charges.size,
        )
    }

    private fun processManualPayout(
        payout: Payout,
        creatorsTier: Tier,
        country: String,
        creatorsVatId: String?,
    ): PayoutItems {
        val feeVatPerCents = feeVatCentsBasedOnCountry(Instant.ofEpochSecond(payout.created), country, creatorsVatId)
        val amountCents = addFeeOnTopOfPayoutAmount(
            payout.amount.toBigDecimal(),
            creatorsTier.feePercents,
            feeVatPerCents,
        )
        val payoutItems = PayoutGroupCounted(
            group = PayoutGroup(
                amountCents = amountCents,
                heroheroFeeCents = amountCents - payout.amount.toBigDecimal(),
                stripeFee = BigDecimal.ZERO,
                stripeFeeFixed = BigDecimal.ZERO,
                currency = Currency.valueOf(payout.currency.uppercase()),
                isRefund = false,
                type = PaymentType.OTHER_TRANSACTION,
                feePerCents = creatorsTier.feePercents,
                fetVatPerCents = feeVatPerCents,
            ),
            count = 1,
        )
        return PayoutItems(
            list = listOf(payoutItems),
            chargesCount = 1,
        )
    }

    private fun generateInvoiceItems(
        payout: Payout,
        payoutItems: PayoutItems,
        country: String,
        creatorsVatId: String?,
    ): List<InvoiceItem> {
        var totalItems = 0

        val invoiceItems = payoutItems
            .list
            .flatMap { item ->
                val group = item.group
                // intentionally adding refunds as positive count to compare with stripe
                totalItems += item.count
                // count of items should be negative for refunds
                val count = item.count * if (group.isRefund) -1 else 1
                val groupPrice = group.amountCents.movePointLeft(2).scale4D()

                val price = groupPrice.times(count.toBigDecimal()).scale4D()
                val priceFeeUnit = group.heroheroFeeCents.movePointLeft(2)
                val priceFee = priceFeeUnit.times(count.toBigDecimal()).scale4D()

                val title = when (group.type) {
                    PaymentType.SUBSCRIPTION -> "Subscription"
                    PaymentType.POST_UNLOCK -> "Post payment"
                    PaymentType.OTHER_TRANSACTION -> "Other incomes or adjustments"
                    PaymentType.COUPON -> "Coupon"
                }

                // we show Tiers (e.g. €5) without decimals, Adjustments (e.g. €12.34) with decimals
                val scale = if (group.type == PaymentType.OTHER_TRANSACTION) 2 else 0
                val vatCents = feeVatCentsBasedOnCountry(Instant.ofEpochSecond(payout.created), country, creatorsVatId)
                val refund = (if (group.isRefund) " refund" else "")
                val centsScaled = group.amountCents.movePointLeft(2).setScale(scale, RoundingMode.HALF_UP)
                val amountString = "${group.currency.symbol}$centsScaled"

                listOf(
                    InvoiceItem(
                        title = "$title " +
                            (if (group.type == PaymentType.OTHER_TRANSACTION) "" else amountString) +
                            refund,
                        priceUnit = groupPrice.movePointRight(2).toInt(),
                        priceUnit4D = groupPrice.movePointRight(4).toInt(),
                        count = count,
                        priceTotal = price.movePointRight(2).toInt(),
                        priceTotal4D = price.movePointRight(4).toInt(),
                        type = InvoiceItemType.TURNOVER,
                        vatCents = 0,
                        // OTHER_TRANSACTION does not directly compare to the original client charges and cannot be directly compared to invoice values
                        comparableToPayout = group.type != PaymentType.OTHER_TRANSACTION,
                    ),
                    InvoiceItem(
                        title = "$title " +
                            (if (group.type == PaymentType.OTHER_TRANSACTION) "" else amountString) +
                            " fee" + refund,
                        priceUnit = priceFeeUnit.movePointRight(2).toInt(),
                        priceUnit4D = priceFeeUnit.movePointRight(4).toInt(),
                        count = count,
                        priceTotal = priceFee.movePointRight(2).toInt(),
                        priceTotal4D = priceFee.movePointRight(4).toInt(),
                        type = InvoiceItemType.FEE,
                        vatCents = vatCents.toInt(),
                        comparableToPayout = false,
                    ),
                ) + if (group.stripeFee != BigDecimal.ZERO) {
                    listOf(
                        InvoiceItem(
                            title = "$title " +
                                (if (group.type == PaymentType.OTHER_TRANSACTION) "" else amountString) +
                                " card fee\u00A02.9%" + refund,
                            priceUnit = group.stripeFee.movePointLeft(2).toInt(),
                            priceUnit4D = group.stripeFee.movePointRight(4).toInt(),
                            count = count,
                            priceTotal = group.stripeFee.times(count.toBigDecimal()).movePointRight(2).toInt(),
                            priceTotal4D = group.stripeFee.times(count.toBigDecimal()).movePointRight(4).toInt(),
                            type = InvoiceItemType.FEE,
                            vatCents = vatCents.toInt(),
                            comparableToPayout = false,
                        ),
                        InvoiceItem(
                            title = "$title " +
                                (if (group.type == PaymentType.OTHER_TRANSACTION) "" else amountString) +
                                " card fee 30c" + refund,
                            priceUnit = group.stripeFeeFixed.movePointLeft(2).toInt(),
                            priceUnit4D = group.stripeFeeFixed.movePointRight(4).toInt(),
                            count = count,
                            priceTotal = group.stripeFeeFixed.times(count.toBigDecimal()).movePointRight(2).toInt(),
                            priceTotal4D = group.stripeFeeFixed.times(count.toBigDecimal()).movePointRight(4).toInt(),
                            type = InvoiceItemType.FEE,
                            vatCents = vatCents.toInt(),
                            comparableToPayout = false,
                        ),
                    )
                } else {
                    emptyList()
                }
            }

        if (totalItems != payoutItems.chargesCount) {
            error("Total invoice item $totalItems != stripe charges count ${payoutItems.chargesCount}.")
        }

        return invoiceItems
    }

    private fun addFeeOnTopOfPayoutAmount(
        amountCents: BigDecimal,
        feeCents: BigDecimal,
        feeVatPerCents: BigDecimal,
    ): BigDecimal {
        // Stripe's `other_adjustment` appears when some manual change is requested. E.g. converting CZK to EUR or vice versa.
        // At this point, we have no links to original supporters' transactions, and we know only the payout value.
        // The fee is then computed ON TOP of the payout, because it has already been deducted.
        // E.g. batch payout € 687 will have 687 * 100 / (100 - feeInCents - feeInCents * feeVatCents / 100)
        //                                     = 687 * 100 / (100 - 10 - 2.1) = € 781.56
        return amountCents
            .times(BigDecimal(100))
            .div(
                BigDecimal(100)
                    .minus(feeCents)
                    .minus(feeCents.times(feeVatPerCents).divide(100.toBigDecimal())),
            )
    }

    private fun assertSingleCurrency(
        payoutId: String,
        accountId: String,
        payoutItems: PayoutItems,
    ): Currency {
        if (payoutItems.list.map { it.group.currency }.distinct().size != 1) {
            error("Stripe payout $payoutId of account $accountId contained multiple currencies, which is not allowed.")
        }
        return payoutItems.list.first().group.currency
    }

    internal fun processBalanceTransaction(
        it: BalanceTransaction,
        accountId: String,
        payoutId: String,
        creatorsTier: Tier,
        creatorsVatId: String?,
        country: String,
        currency: Currency,
    ): PayoutGroup {
        when {
            it.reportingCategory !in setOf("charge", "refund", "other_adjustment") -> {
                error(
                    "Unexpected reporting category '${it.reportingCategory}' in " +
                        " https://dashboard.stripe.com/acct_1RJJXpBOSXndGBvF/payments/py_1RNn7BBOSXndGBvFXaFK7MLE" +
                        " for payout" +
                        " https://dashboard.stripe.com/acct_1RJJXpBOSXndGBvF/payouts/po_1RUzKsBOSXndGBvFrcdG4pwG",
                )
            }
            // The `other_adjustment` might happen when user changes currency
            // of their account - they will receive a payload of money after conversion.
            // In such case we don't display the individual subscriptions, but we just
            // take the 10 % of the whole amount.
            it.reportingCategory == "other_adjustment" -> {
                val fromEurAmountRegex = "from €([\\d,.]+)".toRegex()
                val fromAmountEurMatch = fromEurAmountRegex.find(it.description)
                val toEurAmountRegex = "to €([\\d,.]+)".toRegex()
                val toAmountEurMatch = toEurAmountRegex.find(it.description)
                if (
                    (fromAmountEurMatch == null || fromAmountEurMatch.groupValues.size != 2) &&
                    (toAmountEurMatch == null || toAmountEurMatch.groupValues.size != 2)
                ) {
                    // we currently only support EUR here, we might need to rework this once we charge in USD
                    error(
                        "Cannot match EUR amount in transaction description of '${it.reportingCategory}'" +
                            " for ${it.currency.uppercase()} ${it.amount.toBigDecimal().movePointLeft(2)}" +
                            " – good luck with that: ${it.description}",
                    )
                }

                if (it.currency.uppercase() != Currency.EUR.name) {
                    error("Not ready for currency: ${it.currency}")
                }

                val signFromTransaction = if (it.amount < 0) BigDecimal(-1) else BigDecimal(1)

                val amountEurUnsigned =
                    fromAmountEurMatch?.groupValues?.getOrNull(1)
                        ?.replace("[.,]".toRegex(), "")
                        ?.toBigDecimalOrNull()
                        ?: toAmountEurMatch?.groupValues?.getOrNull(1)
                            ?.replace("[.,]".toRegex(), "")
                            ?.toBigDecimalOrNull()
                        ?: error("Cannot convert ${it.description} to Long value.")

                val feeVatPerCents =
                    feeVatCentsBasedOnCountry(Instant.ofEpochSecond(it.created), country, creatorsVatId)

                val originalAmountBeforeCommission = signFromTransaction * addFeeOnTopOfPayoutAmount(
                    amountEurUnsigned,
                    creatorsTier.feePercents,
                    feeVatPerCents,
                )

                log.info(
                    "Processing ${if (signFromTransaction < BigDecimal.ZERO) "negative" else "positive"}" +
                        " 'other_adjustment' of '${it.description}'.",
                )
                val amountEur = amountEurUnsigned * signFromTransaction
                return PayoutGroup(
                    amountCents = originalAmountBeforeCommission,
                    heroheroFeeCents = originalAmountBeforeCommission - amountEur,
                    // we cannot correctly compute stripeFee for adjustment transactions, leaving to zero
                    // – will be part of heroheroFeeCents in full
                    stripeFee = BigDecimal.ZERO,
                    stripeFeeFixed = BigDecimal.ZERO,
                    currency = currency,
                    isRefund = false,
                    type = PaymentType.OTHER_TRANSACTION,
                    feePerCents = creatorsTier.feePercents,
                    fetVatPerCents = feeVatPerCents,
                )
            }
            // This is a regular charge.
            it.sourceObject is Charge -> {
                val sourceObject = (it.sourceObject as Charge)
                val sourceTransfer = sourceObject.sourceTransferObject

                val originalCharge: Charge = sourceTransfer.sourceTransactionObject
                    ?: sourceTransfer.transferGroup?.let { group -> stripe.getOriginalChargeByGroup(group, currency) }
                    ?: error(
                        "Source transaction object was missing for ${sourceObject.sourceTransfer}: " +
                            sourceTransfer.metadata,
                    )

                val (heroheroFees, stripeFee, stripeFeeFixed) = computeChargedFeeCents(originalCharge)

                return PayoutGroup(
                    amountCents = originalCharge.amount!!.toBigDecimal(),
                    heroheroFeeCents = heroheroFees,
                    stripeFee = stripeFee,
                    stripeFeeFixed = stripeFeeFixed,
                    currency = Currency.valueOf(originalCharge.currency.uppercase()),
                    isRefund = false,
                    type = originalCharge.metadata["type"]
                        ?.let { PaymentType.valueOf(it.replace("-", "_").uppercase()) }
                        ?: PaymentType.SUBSCRIPTION,
                    feePerCents = heroheroFees
                        .divide(originalCharge.amount.toBigDecimal(), 2, RoundingMode.HALF_UP)
                        .times(BigDecimal(100)),
                    fetVatPerCents = feeVatCentsBasedOnCountry(
                        Instant.ofEpochSecond(it.created),
                        country,
                        creatorsVatId,
                    ),
                )
            }
            // Regular refund.
            it.sourceObject is Refund -> {
                val sourceObject = (it.sourceObject as Refund)
                val sourceTransfer = sourceObject
                    .chargeObject
                    .sourceTransferObject

                // sourceTransaction would be 4th level expansion, so it cannot be fetched during listPayoutTransactions
                val sourceTransaction = sourceTransfer.sourceTransaction?.let { stripe.charge(it, currency, null) }

                val originalCharge: Charge = sourceTransaction
                    ?: sourceTransfer.transferGroup?.let { group -> stripe.getOriginalChargeByGroup(group, currency) }
                    ?: error(
                        "Source transaction object was missing for ${sourceObject.chargeObject.sourceTransfer}:" +
                            " ${sourceTransfer.metadata}",
                    )

                val (heroheroFees, stripeFee, stripeFeeFixed) = computeChargedFeeCents(originalCharge)

                return PayoutGroup(
                    // note that refunds are handled by count, amount must stay positive for refunds as well
                    amountCents = originalCharge.amount!!.toBigDecimal(),
                    heroheroFeeCents = heroheroFees,
                    stripeFee = stripeFee,
                    stripeFeeFixed = stripeFeeFixed,
                    currency = Currency.valueOf(originalCharge.currency.uppercase()),
                    isRefund = true,
                    type = originalCharge.metadata["type"]
                        ?.let { PaymentType.valueOf(it.replace("-", "_").uppercase()) }
                        ?: PaymentType.SUBSCRIPTION,
                    feePerCents = heroheroFees
                        .divide(originalCharge.amount.toBigDecimal(), 2, RoundingMode.HALF_UP)
                        .times(BigDecimal(100)),
                    fetVatPerCents = feeVatCentsBasedOnCountry(
                        Instant.ofEpochSecond(it.created),
                        country,
                        creatorsVatId,
                    ),
                )
            }

            else ->
                error("Cannot process non-charge and non-refund: ${it.source}")
        }
    }

    /**
     * Method computes charged Herohero fee from the sourceTransaction and its transfer data. E.g.:
     * - `originalCharge.amount = 700` (€ 7.00)
     * - `transfer.amount       = 630` (€ 6.30)
     * - `feeCents              = 070` (€ 0.70)
     */
    private fun computeChargedFeeCents(originalCharge: Charge): Triple<BigDecimal, BigDecimal, BigDecimal> =
        if (originalCharge.transferData.amount == null) {
            if (originalCharge.applicationFeeAmount == null) {
                error(
                    "Both `sourceTransaction.transferData.amount` and `sourceTransaction.applicationFeeAmount`" +
                        " cannot be `null` for ${originalCharge.id}",
                )
            }
            val isSplitFeeStructure = originalCharge.metadata["appFeeHerohero"] != null
            if (isSplitFeeStructure) {
                val heroheroFee = originalCharge.metadata["appFeeHerohero"]?.toBigDecimal()
                    ?: error("Charge ${originalCharge.id} did not contain necessary fields.")
                val stripeFeeDynamic = originalCharge.metadata["appFeeStripeDynamic"]?.toBigDecimal()
                    ?: error("Charge ${originalCharge.id} did not contain necessary fields.")
                val stripeFeeFixed = originalCharge.metadata["appFeeStripeFixed"]?.toBigDecimal()
                    ?: error("Charge ${originalCharge.id} did not contain necessary fields.")
                Triple(heroheroFee.movePointRight(2), stripeFeeDynamic, stripeFeeFixed)
            } else {
                Triple(originalCharge.applicationFeeAmount.toBigDecimal(), BigDecimal.ZERO, BigDecimal.ZERO)
            }
        } else {
            val transferAmount = originalCharge.transferData.amount
            val chargedFee = originalCharge.amount.toBigDecimal() - transferAmount.toBigDecimal()
            if (chargedFee == BigDecimal.ZERO) {
                error("Herohero acquired fee should never be zero for charge ${originalCharge.id}.")
            }
            // Non-100% transfers (used in CZ because of currency conversion avoidance) never contains
            // Stripe fees – these are purely 10% Herohero fee taken as a 90% transfer.
            Triple(chargedFee, BigDecimal.ZERO, BigDecimal.ZERO)
        }

    private fun generateInvoice(
        user: User,
        accountId: String,
        payout: Payout,
        currencyInvoice: Currency,
        countryOfDestination: String,
        createdAt: Instant,
        invoiceItems: List<InvoiceItem>,
        euReverseCharged: Boolean,
    ): Invoice {
        val heroheroCompany = usersCollection["infoheroherokamniiih"].get().company
            ?: error("Missing `company` object for Herohero user")
        val totalTurnover = invoiceItems.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.priceTotal }.toLong()
        val totalTurnover4D = invoiceItems.filter {
            it.type == InvoiceItemType.TURNOVER
        }.sumOf { it.priceTotal4D ?: (it.priceTotal * 100) }.toLong()
        val totalFees = invoiceItems.filter { it.type == InvoiceItemType.FEE }.sumOf { it.priceTotal }.toLong()
        val totalFees4D = invoiceItems.filter {
            it.type == InvoiceItemType.FEE
        }.sumOf { it.priceTotal4D ?: (it.priceTotal * 100) }.toLong()
        val invoice = Invoice(
            invoiceId = nextInvoiceId(createdAt, payout.id),
            userId = user.id,
            stripeAccountId = accountId,
            stripePayoutId = payout.id,
            timestamp = createdAt,
            total = totalTurnover - totalFees,
            total4D = totalTurnover4D - totalFees4D,
            currency = Currency.valueOf(payout.currency.uppercase()),
            currencyInvoice = currencyInvoice,
            countryOfDestination = countryOfDestination,
            items = invoiceItems,
            invoicedCompany = user.companyOrPhysicalPerson,
            issuingCompany = heroheroCompany,
            euOneStopShop = isOneStopShop(countryOfDestination, user.companyOrPhysicalPerson.vatId),
            euReverseCharged = euReverseCharged,
            // TODO maybe later use directly https://herohero.flexibee.eu/c/herohero/kurz.json
            eurConversionRateCents = null,
        )

        // make sure that invoice total equals to payout total, unfortunately amount comparison
        // can only be done for payouts done in the same currency as the original charge (and then our created invoice)
        if (invoice.total != payout.amount &&
            invoice.currencyPayout == invoice.currencyInvoice &&
            invoice.items.all { it.comparableToPayout }
        ) {
            error(
                "Invalid amount generated for invoice ${invoice.id} and payout ${payout.id}/$accountId:" +
                    " ${invoice.total} != ${payout.amount}, diff ${payout.amount - invoice.total}.",
            )
        }
        return invoice
    }

    internal fun storeInvoice(
        invoice: Invoice,
        user: User,
        overwriteInvoice: Boolean,
        sendEmail: Boolean,
    ) {
        // and if the invoice already exists, then we might not want to overwrite it and we are done
        if (invoice.id in invoicesCollection) {
            log.error("Invoice ${invoice.id} already exists.", mapOf("userId" to user.id))
            if (!overwriteInvoice) {
                return
            }
        }
        log.info(
            "Writing invoice: " +
                invoiceLink(hostnameServices, user.id, invoice.invoiceId, invoice.authToken, user.language) +
                " for payout" +
                " https://dashboard.stripe.com/${invoice.stripeAccountId}/payouts/${invoice.stripePayoutId}." +
                " With VAT: ${user.companyOrPhysicalPerson.vatType}",
            mapOf("userId" to user.id),
        )
        invoicesCollection[invoice.id].set(invoice)

        val invoices = invoicesCollection.where(Invoice::userId).isEqualTo(user.id).count()
        usersCollection[user.id]
            .field(root(User::counts).path(SupportCounts::invoices))
            .update(invoices)

        // and send email if needed
        if (user.email != null && sendEmail) {
            sendEmail(invoice, user)
        }
    }

    /** try to generate a unique invoiceId to avoid duplicties */
    fun nextInvoiceId(
        timestamp: Instant,
        payoutId: String,
        seed: Int = payoutId.hashCode(),
    ): String {
        val rand = Random(seed)
        val yearMonth = timestamp.atZone(ZoneOffset.UTC).format("yyyyMM")
        while (true) {
            val suffix = rand.nextLong(100000, 999999)
            val invoiceId = "$yearMonth-$suffix"
            val existingInvoice = invoicesCollection.where(Invoice::invoiceId).isEqualTo(invoiceId).fetchSingle()
            if (existingInvoice != null && existingInvoice.stripePayoutId != payoutId) {
                // invoice with this id (but different payout) already exists, let's continue
                continue
            }
            return invoiceId
        }
    }

    internal fun getInvoiceForPayout(payoutId: String): Invoice? =
        invoicesCollection
            .where(Invoice::stripePayoutId)
            .isEqualTo(payoutId)
            .fetchSingle()

    private fun sendEmail(
        invoice: Invoice,
        user: User,
    ) {
        pubSub.publish(
            EmailPublished(
                // we must be able to send invoices even for "deleted" users with intentionally
                // broken emails like: `<EMAIL>-2023-12-29T19:06:18.749532233Z`
                to = user.email!!.restoreEmail(),
                template = "new-invoice",
                variables = listOf(
                    "user-name" to user.name,
                    "invoice-id" to invoice.invoiceId,
                    "invoice-link" to
                        invoiceLink(hostnameServices, user.id, invoice.invoiceId, invoice.authToken, user.language),
                    "invoice-report-link" to
                        invoiceReportLink(hostnameServices, user.id, invoice.invoiceId, invoice.authToken),
                ),
                language = user.language,
            ),
        )
    }

    private fun BigDecimal.scale4D(): BigDecimal = this.setScale(4, RoundingMode.HALF_UP)

    /** returns VAT for transactions where Herohero was already a VAT payer and only for CZ creators */
    private fun feeVatCentsBasedOnCountry(
        instant: Instant,
        country: String,
        vatId: String?,
    ): BigDecimal =
        if (
            // herohero was not always a VAT payer
            instant > heroheroVatPayerSince &&
            // https://linear.app/herohero/issue/HH-1353/only-eu-countries-should-be-charged-with-vat
            (country.uppercase() == CZ_VAT_COUNTRY || isOneStopShop(country, vatId))
        )
            countryToVatMapping[country, instant].toBigDecimal()
        else
            BigDecimal.ZERO

    /** returns if the generated invoice should be created in One-stop-shop state*/
    private fun isOneStopShop(
        country: String,
        vatId: String?,
    ): Boolean =
        country.uppercase() != CZ_VAT_COUNTRY &&
            country.uppercase() in euCountries &&
            vatId.nullIfEmpty() == null

    private fun findByConnectedAccountId(accountId: String): User? =
        usersCollection
            .where(root(User::creator).path(Creator::stripeAccountId)).isEqualTo(accountId)
            .fetchSingle()
            ?: usersCollection
                .where(root(User::creator).path(Creator::stripeAccountLegacyIds)).contains(accountId)
                .fetchSingle()
}

data class PayoutItems(
    val list: List<PayoutGroupCounted>,
    val chargesCount: Int,
)

data class PayoutGroupCounted(
    val group: PayoutGroup,
    val count: Int,
)

data class PayoutGroup(
    val amountCents: BigDecimal,
    val heroheroFeeCents: BigDecimal,
    val stripeFee: BigDecimal,
    val stripeFeeFixed: BigDecimal,
    val currency: Currency,
    val isRefund: Boolean,
    val type: PaymentType,
    val feePerCents: BigDecimal,
    val fetVatPerCents: BigDecimal,
)

private fun invoiceLink(
    hostnameServices: String,
    userId: String,
    invoiceId: String,
    authToken: String,
    locale: String,
) = hostnameServices.replace(".herohero.co", "-na.herohero.co") +
    "/invoice-generator/?invoiceId=$invoiceId&userId=$userId&authToken=$authToken&locale=$locale"

// move this to some common place, right now is duplicated in hero.api.invoice.util
private fun invoiceReportLink(
    hostnameServices: String,
    userId: String,
    invoiceId: String,
    authToken: String,
) = "$hostnameServices/api/v1/users/$userId/invoices/$invoiceId/report?authToken=$authToken"

data class ProcessPayout(
    val accountId: String,
    val payoutId: String,
    val currency: Currency,
    val overwriteInvoice: Boolean,
    val sendEmail: Boolean,
    /** @see <a href="https://linear.app/herohero/issue/HH-3158/silently-ignoring-other-adjustments-if-needed">HH-3158</a> */
    val skipOtherAdjustments: Boolean = false,
)
