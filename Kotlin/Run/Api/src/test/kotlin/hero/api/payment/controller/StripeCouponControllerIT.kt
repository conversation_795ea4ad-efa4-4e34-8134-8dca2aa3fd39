package hero.api.payment.controller

import com.fasterxml.jackson.module.kotlin.readValue
import com.stripe.model.Coupon
import com.stripe.model.PaymentMethod
import com.stripe.param.CustomerCreateParams
import com.stripe.param.CustomerUpdateParams
import com.stripe.param.PaymentIntentListParams
import com.stripe.param.PaymentMethodCreateParams
import com.stripe.param.PriceCreateParams
import com.stripe.param.SubscriptionListParams
import hero.api.payment.controller.dto.CouponInviteRequest
import hero.api.payment.controller.dto.CouponPurchaseRequest
import hero.api.payment.controller.dto.CouponResponseDto
import hero.api.payment.controller.dto.CouponsResponseDto
import hero.api.payment.service.StripePaymentsService
import hero.api.subscriber.repository.PaymentIntentStatus
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.subscriber.repository.SubscribersRepository
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.SystemEnv
import hero.baseutils.instantOf
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.baseutils.toYearMonth
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.PubSub
import hero.gcloud.typedCollectionOf
import hero.http4k.auth.jwtFor
import hero.http4k.auth.withAccessTokenCookie
import hero.jackson.jackson
import hero.jackson.toJson
import hero.model.Currency
import hero.model.Invoice
import hero.model.Path
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SupportCounts
import hero.model.Tier
import hero.model.User
import hero.model.topics.CardCreateType
import hero.model.topics.EmailPublished
import hero.repository.user.UserRepository
import hero.stripe.model.StripePrice
import hero.stripe.service.AppleSubscriptionService
import hero.stripe.service.StripeAccountService
import hero.stripe.service.StripeClients
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriberSaver
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import hero.test.IntegrationTest
import hero.test.StripeHelper
import hero.test.euStripeConnectedAccount
import hero.test.gcloud.FirestoreTestDatabase
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.collections.set
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class StripeCouponControllerIT : IntegrationTest() {
    private val isProduction = false
    private val pubSub: PubSub = mockk(relaxed = true)
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val userRepositoryPG = mockk<UserRepository>()
    private val stripeHelper = StripeHelper(stripeClients[Currency.EUR])
    private val stripeService = spyk(StripeService(stripeClients, pubSub))
    private val stripePaymentMethods = spyk(StripePaymentMethodsService(stripeClients, stripeService, pubSub))
    private val stripeAccountService: StripeAccountService = mockk()
    private val tierRepository: TiersRepository = mockk()
    private val usersCollection = firestore.typedCollectionOf(User)
    private val pathsCollection = firestore.typedCollectionOf(Path)

    private val userRepository: UsersRepository = spyk(
        UsersRepository(
            firestoreFulltext = mockk(),
            imageRepository = mockk(),
            pathCollection = pathsCollection,
            pubSub = pubSub,
            subscriberCollection = mockk(),
            tierRepository = tierRepository,
            accountService = stripeAccountService,
            collection = usersCollection,
            userIdGenerator = mockk(),
            userRepository = userRepositoryPG,
        ),
    )

    private val subscriberRepository: SubscribersRepository = SubscribersRepository(
        collection = firestore.typedCollectionOf(Subscriber),
        pubSub = pubSub,
    )

    private val countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()
    private val subscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        paymentMethodsService = stripePaymentMethods,
        production = isProduction,
        countryToVatMapping = countryToVatMapping,
        recurringPeriod = PriceCreateParams.Recurring.Interval.MONTH,
    )

    private val stripeSubscriberSaver: StripeSubscriberSaver = StripeSubscriberSaver(
        firestore.typedCollectionOf(Subscriber),
        firestore.typedCollectionOf(Tier),
        AppleSubscriptionService(mockk(), false),
        pubSub,
    )
    private val subscriberStripeRepository: SubscriberStripeRepository = spyk(
        SubscriberStripeRepository(
            stripe = stripeService,
            stripePricesCollection = firestore.typedCollectionOf(StripePrice),
            subscriberRepository = subscriberRepository,
            tierRepository = tierRepository,
            userRepository = userRepository,
            subscriptionService = subscriptionService,
            firestore = FirestoreTestDatabase.testFirestore,
            stripeSubscriberSaver = stripeSubscriberSaver,
            lazyContext = lazyTestContext,
        ),
    )

    private val stripePaymentsService: StripePaymentsService = StripePaymentsService(
        tierRepository = tierRepository,
        postPaymentsCollection = mockk(),
        stripe = stripeService,
        subscriberStripeRepository = subscriberStripeRepository,
        pubSub = pubSub,
        hostname = "https://local.herohero.co",
        hostnameServices = "https://svc-devel.herohero.co",
        countryToVatMapping = countryToVatMapping,
    )

    private val invoicesCollection = firestore.typedCollectionOf(Invoice)

    private val stripeController = StripeController(
        production = isProduction,
        hostname = "https://local.herohero.co",
        stripe = stripeService,
        stripePaymentMethods = stripePaymentMethods,
        subscriberRepository = subscriberRepository,
        subscriberStripeRepository = subscriberStripeRepository,
        tierRepository = tierRepository,
        userRepository = userRepository,
        categoriesRepository = mockk(),
        stripePublicKeys = mapOf(),
        stripePaymentsService = stripePaymentsService,
        stripeAccountService = stripeAccountService,
        subscriptionService = subscriptionService,
        countryToVatMapping = countryToVatMapping,
        cancelSubscriptionCommandService = mockk(),
        subscribersCollection = mockk(),
        invoicesCollection = invoicesCollection,
    )

    private val couponController = StripeCouponController(
        tierRepository = tierRepository,
        stripePaymentsService = stripePaymentsService,
        stripeService = stripeService,
        userRepository = userRepository,
    )

    private fun testUser(
        creator: Boolean,
        tierId: String,
    ): User =
        testHelper
            .createUser(
                id = "test-${if (creator) "creator" else "subscriber"}-" +
                    UUID.randomUUID().toString().lowercase().replace("[^a-z]+".toRegex(), ""),
                stripeAccountId = if (creator) euStripeConnectedAccount else null,
                tierId = tierId,
            )
            .copy(counts = SupportCounts(supporters = 50, incomes = 1000, invoices = 3))

    @AfterAll
    fun afterAll() {
        stripeHelper.testCleanUp()
    }

    @Test
    fun `creatorId must match to the coupon's metadata`() {
        val testTier = Tier.ofId("EUR05")
        val couponId = "AAAAAAAAAAA"

        val user = testUser(false, testTier.id)
        every { userRepository.get(user.id) } returns user
        every { stripeService.getCouponOrNull(couponId, testTier.currency) } returns Coupon().also {
            it.metadata = mapOf("creatorId" to "some-other")
        }

        assertThrows<NotFoundException> {
            couponController.routeGetCoupon(
                Request(Method.GET, "/v1/users/${user.id}/coupons/$couponId"),
            )
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `create coupon after successful payment`(creatorsCoupon: Boolean) {
        // 1. purchase a coupon
        // 2. use a coupon to pay a subscription
        // 3. advance 3 months to see that subscription will need a payment method
        every { userRepositoryPG.save(any()) } returns testHelper.createUser()
        every { stripeAccountService.updateAccount(any()) } just runs
        val testTier = Tier.ofId("EUR05")
        val testHigherTier = Tier.ofId("EUR07")

        val user = testUser(false, testTier.id)
        val subscriber = testUser(false, testTier.id)
        val creator = testUser(true, testTier.id)
        val otherCreator = testUser(true, testTier.id)

        val customerForVoucher = stripeClients[testTier.currency].customers().create(
            CustomerCreateParams
                .builder()
                .setEmail(user.email)
                .setName("Herohero testing account for purchasing voucher")
                .build(),
        )

        val customerForSubscription = stripeClients[testTier.currency].customers().create(
            CustomerCreateParams
                .builder()
                .setEmail(subscriber.email)
                .setName("Herohero testing account for creating subscription with voucher")
                .build(),
        )

        // TODO create if missing
        user.customerIds[testTier.currency.name] = customerForVoucher.id
        subscriber.customerIds[testTier.currency.name] = customerForSubscription.id

        userRepository.store(user)
        userRepository.store(subscriber)
        userRepository.store(creator)
        userRepository.store(otherCreator)

        every { tierRepository.get(testTier.id) } returns testTier
        every { tierRepository.get(testHigherTier.id) } returns testHigherTier

        val paymentMethodForCoupon = paymentMethod(customerForVoucher.id!!, true, testTier.currency)

        val requestForCoupon = Request(
            Method.POST,
            if (creatorsCoupon) "/v1/users/${creator.id}/coupons" else "/v1/coupons",
        )
            .withAccessTokenCookie(jwtFor(user.id))
            .body(
                CouponPurchaseRequest(
                    paymentMethodId = paymentMethodForCoupon.id,
                    months = 2,
                ).toJson(),
            )

        val slot = slot<EmailPublished>()
        every { pubSub.publish(capture(slot)) } just runs

        val responseForCoupon = if (creatorsCoupon) {
            couponController.routePostPurchaseCreatorsCoupon(requestForCoupon)
        } else {
            // TODO needs specification, see when it was removed:
            // https://gitlab.com/heroheroco/backend/-/merge_requests/743/diffs?commit_id=f6b38f6e721328c49791ee7993752a0705d6f42e
            return
        }

        assertEquals(Status.OK, responseForCoupon.status)
        val couponIntent = stripeClients[testTier.currency].paymentIntents()
            .list(PaymentIntentListParams.builder().setCustomer(customerForVoucher.id).build())
            .data
            .firstOrNull()
        assertNotNull(couponIntent)
        val bodyWithCoupon = jackson.readValue<PaymentResponse>(responseForCoupon.bodyString())
        val coupon = stripeClients[testTier.currency].coupons().retrieve(bodyWithCoupon.attributes.couponCode)
        // for some reason, I could not make mockk's verify to work, comparing at least captured slot
        assertEquals("coupon-paid", slot.captured.template)
        // the coupon id must be correctly stored within its payment intent
        assertEquals(coupon.id, couponIntent.metadata["couponId"])

        // creator's coupons are transferred immediately to the creator, our coupons to our temporary account
        assertEquals(
            if (creatorsCoupon)
                creator.creator.stripeAccountId
            else
                error("Herohero coupons not implemented."),
            couponIntent.transferData.destination,
        )
        // creator's coupon receive only the amount relevant to its country, vat status and herohero fee
        assertEquals(
            if (creatorsCoupon)
                BigDecimal(coupon.durationInMonths * testTier.priceCents).times("0.879".toBigDecimal()).toLong()
            else
                testTier.priceCents,
            couponIntent.transferData.amount,
        )

        val requestForCouponCheck = Request(Method.GET, "/v1/users/${creator.id}/coupons/${coupon.id}")
        val responseForCouponCheck = couponController.routeGetCoupon(requestForCouponCheck)
        assertEquals(Status.OK, responseForCouponCheck.status)
        val bodyWithCouponCheck = jackson.readValue<CouponResponseDto>(responseForCouponCheck.bodyString())
        assertEquals(coupon.id, bodyWithCouponCheck.data.id)
        assertEquals(creator.id, bodyWithCouponCheck.data.relationships.creator.id)

        // now apply the coupon above for a wrong creator
        val badRequestForSubscription = Request(
            Method.POST,
            "/v1/users/${subscriber.id}/subscriptions/${otherCreator.id}/payments",
        )
            .withAccessTokenCookie(jwtFor(subscriber.id))
            .body(
                CreateSubscriptionPostBody(
                    couponId = bodyWithCoupon.attributes.couponCode,
                    paymentMethodId = null,
                ).toJson(),
            )

        // and it must be denied
        assertThrows<ForbiddenException> {
            stripeController.routeCreateSubscription(badRequestForSubscription)
        }

        // now we set more expensive tier to the creator to prove
        // original tier still exists
        creator.creator.tierId = testHigherTier.id
        userRepository.store(creator)

        // now apply the coupon purchase above for the right creator
        val requestForSubscription = { userId: String ->
            Request(Method.POST, "/v1/users/$userId/subscriptions/${creator.id}/payments")
                .withAccessTokenCookie(jwtFor(userId))
                .body(
                    CreateSubscriptionPostBody(
                        couponId = bodyWithCoupon.attributes.couponCode,
                        paymentMethodId = null,
                    ).toJson(),
                )
        }

        val responseForSubscription = stripeController.routeCreateSubscription(requestForSubscription(subscriber.id))

        assertEquals(Status.OK, responseForSubscription.status)
        val bodyWithSubscription = jackson.readValue<PaymentResponse>(responseForSubscription.bodyString())
        assertEquals(PaymentIntentStatus.SUCCEEDED, bodyWithSubscription.attributes.status)
        assertEquals(SubscriberStatus.ACTIVE, bodyWithSubscription.attributes.subscriptionStatus)

        // user, without a subscription, using the same coupon does not throw because the user already has
        // an active subscription (from the applied coupon)
        assertDoesNotThrow {
            stripeController.routeCreateSubscription(requestForSubscription(subscriber.id))
        }

        with(subscriberRepository.getSubscriber(subscriber.id, creator.id)) {
            assertNotNull(this)
            val expectedLocalDate = LocalDate.now().plusMonths(2)
            val actualExpiresAt = LocalDate.ofInstant(couponExpiresAt, ZoneOffset.UTC)
            assertEquals(expectedLocalDate, actualExpiresAt)
            assertNull(couponExpiresNotifiedAt)
        }

        // user, without a subscription, attempting to redeemed used coupon gets an exception
        assertThrows<ConflictException> {
            stripeController.routeCreateSubscription(requestForSubscription(user.id))
        }

        var subscription = stripeClients[testTier.currency].subscriptions()
            .list(SubscriptionListParams.builder().setCustomer(subscriber.customerIds[testTier.currency.name]).build())
            .data
            .firstOrNull()

        assertNotNull(subscription)
        log.info("Created subscription ${subscription.id} paid by coupon ${bodyWithCoupon.attributes.couponCode}")

        assertEquals(coupon.durationInMonths, subscription.metadata[Subscriber::couponAppliedForMonths.name]?.toLong())
        assertEquals("active", subscription.status)
        // the subscription must be created with the tier associated with the coupon (not the increased price)
        assertEquals(testTier.priceCents.toLong(), subscription.items.data.first().price.unitAmount)
    }

    @Test
    @Disabled("We must first define how the general vouchers will work.")
    fun `create herohero coupon after successful payment`() {
        // 1. purchase a coupon
        // 2. use a coupon to pay a subscription
        // 3. advance 3 months to see that subscription will need a payment method
        val dayOne = instantOf("2020-01-01T12:00:00Z")

        // this allows us simulate progress of subscriptions
        val testClock = stripeHelper.testClock(dayOne)

        every { stripeAccountService.updateAccount(any()) } just runs
        val testTier = Tier.ofId("EUR05")

        val user = testUser(false, testTier.id)
        val subscriber = testUser(false, testTier.id)
        val creator = testUser(true, testTier.id)
        val otherCreator = testUser(true, testTier.id)

        val customerForVoucher = stripeClients[testTier.currency].customers().create(
            CustomerCreateParams
                .builder()
                .setEmail(user.email)
                .setName("Herohero testing account for purchasing voucher")
                .setTestClock(testClock.id)
                .build(),
        )

        val customerForSubscription = stripeClients[testTier.currency].customers().create(
            CustomerCreateParams
                .builder()
                .setEmail(subscriber.email)
                .setName("Herohero testing account for creating subscription with voucher")
                .setTestClock(testClock.id)
                .build(),
        )

        // TODO create if missing
        user.customerIds[testTier.currency.name] = customerForVoucher.id
        subscriber.customerIds[testTier.currency.name] = customerForSubscription.id

        userRepository.store(user)
        userRepository.store(subscriber)
        userRepository.store(creator)
        userRepository.store(otherCreator)

        every { tierRepository.get(testTier.id) } returns testTier

        val paymentMethodForCoupon = paymentMethod(customerForVoucher.id!!, true, testTier.currency)

        val requestForCoupon = Request(Method.POST, "/v1/coupons")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(
                CouponPurchaseRequest(
                    paymentMethodId = paymentMethodForCoupon.id,
                    months = 2,
                ).toJson(),
            )

        val slot = slot<EmailPublished>()
        every { pubSub.publish(capture(slot)) } just runs

        val responseForCoupon = couponController.routePostPurchaseCreatorsCoupon(requestForCoupon)

        assertEquals(Status.OK, responseForCoupon.status)
        val couponIntent = stripeClients[testTier.currency].paymentIntents()
            .list(PaymentIntentListParams.builder().setCustomer(customerForVoucher.id).build())
            .data
            .firstOrNull()
        assertNotNull(couponIntent)
        val bodyWithCoupon = jackson.readValue<PaymentResponse>(responseForCoupon.bodyString())
        val coupon = stripeClients[testTier.currency].coupons().retrieve(bodyWithCoupon.attributes.couponCode)
        // for some reason, I could not make mockk's verify to work, comparing at least captured slot
        assertEquals("coupon-paid", slot.captured.template)
        // the coupon id must be correctly stored within its payment intent
        assertEquals(coupon.id, couponIntent.metadata["couponId"])
        assertEquals("heroheroCouponAccountId", couponIntent.transferData.destination)
        assertEquals(couponIntent.amount, couponIntent.transferData.amount)

        val requestForCouponCheck = Request(Method.GET, "/v1/users/${subscriber.id}/coupons/${coupon.id}")
        val responseForCouponCheck = couponController.routeGetCoupon(requestForCouponCheck)
        assertEquals(Status.OK, responseForCouponCheck.status)
        val bodyWithCouponCheck = jackson.readValue<CouponResponseDto>(responseForCouponCheck.bodyString())
        assertEquals(coupon.id, bodyWithCouponCheck.data.id)
        assertEquals(creator.id, bodyWithCouponCheck.data.relationships.creator.id)

        // now apply the coupon above for a wrong creator
        val badRequestForSubscription = Request(
            Method.POST,
            "/v1/users/${subscriber.id}/subscriptions/${otherCreator.id}/payments",
        )
            .withAccessTokenCookie(jwtFor(subscriber.id))
            .body(
                CreateSubscriptionPostBody(
                    couponId = bodyWithCoupon.attributes.couponCode,
                    paymentMethodId = null,
                ).toJson(),
            )

        // and it must be denied
        assertThrows<ForbiddenException> {
            stripeController.routeCreateSubscription(badRequestForSubscription)
        }

        // now apply the coupon purchase above for the right creator
        val requestForSubscription = Request(
            Method.POST,
            "/v1/users/${subscriber.id}/subscriptions/${creator.id}/payments",
        )
            .withAccessTokenCookie(jwtFor(subscriber.id))
            .body(
                CreateSubscriptionPostBody(
                    couponId = bodyWithCoupon.attributes.couponCode,
                    paymentMethodId = null,
                ).toJson(),
            )

        val responseForSubscription = stripeController.routeCreateSubscription(requestForSubscription)

        assertEquals(Status.OK, responseForSubscription.status)
        val bodyWithSubscription = jackson.readValue<PaymentResponse>(responseForSubscription.bodyString())
        assertEquals(PaymentIntentStatus.SUCCEEDED, bodyWithSubscription.attributes.status)
        assertEquals(SubscriberStatus.ACTIVE, bodyWithSubscription.attributes.subscriptionStatus)

        // coupon cannot be applied twice
        assertThrows<ConflictException> {
            stripeController.routeCreateSubscription(requestForSubscription)
        }

        var subscription = stripeClients[testTier.currency].subscriptions()
            .list(SubscriptionListParams.builder().setCustomer(subscriber.customerIds[testTier.currency.name]).build())
            .data
            .firstOrNull()

        assertNotNull(subscription)
        log.info("Created subscription ${subscription.id} paid by coupon ${bodyWithCoupon.attributes.couponCode}")

        assertEquals(coupon.durationInMonths, subscription.metadata[Subscriber::couponAppliedForMonths.name]?.toLong())
        assertEquals("active", subscription.status)
    }

    @Test
    fun `refused payment does not produce any coupon`() {
        val testTier = Tier.ofId("EUR05")
        val user = testUser(false, testTier.id)
        val creator = testUser(true, testTier.id)

        val customer = stripeClients[testTier.currency].customers().create(
            CustomerCreateParams
                .builder()
                .setEmail(user.email)
                .setName("Herohero testing account")
                .build(),
        )

        every { userRepository.get(user.id) } returns user
        every { userRepository.get(creator.id) } returns creator
        every { tierRepository.get(testTier.id) } returns testTier

        val paymentMethod = paymentMethod(customer.id!!, false, testTier.currency)

        // TODO create if missing
        user.customerIds[testTier.currency.name] = customer.id

        val request = Request(Method.POST, "/v1/users/${creator.id}/coupons")
            .body(
                CouponPurchaseRequest(
                    paymentMethodId = paymentMethod.id,
                    months = 3,
                ).toJson(),
            )

        every { subscriberStripeRepository.customerFactory(user.id, testTier.currency) } returns customer.id
        every { userRepository.get(request) } returns user

        val response = couponController.routePostPurchaseCreatorsCoupon(request)

        assertEquals(Status.UNPROCESSABLE_ENTITY, response.status)
        val body = jackson.readValue<PaymentResponse>(response.bodyString())
        assertTrue(body.attributes.couponCode == null)
    }

    // TODO shift to integration helpers – not trivial because of relation to StripeService
    private fun paymentMethod(
        customerId: String,
        valid: Boolean,
        currency: Currency,
    ): PaymentMethod {
        val paymentMethod = stripeClients[currency].paymentMethods().create(
            PaymentMethodCreateParams
                .builder()
                .setType(PaymentMethodCreateParams.Type.CARD)
                .setCard(
                    PaymentMethodCreateParams.CardDetails.builder()
                        .setNumber(if (valid) "****************" else "****************")
                        .setExpMonth(1)
                        .setExpYear(Instant.now().toYearMonth().year + 2L)
                        .setCvc("123")
                        .build(),
                )
                .build(),
        )
        // we associate the new invalid payment method with the customer
        stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethodId = paymentMethod.id,
            customerId = customerId,
            cardCreateType = CardCreateType.CARD,
            confirm = true,
            currency = currency,
            makeDefault = true,
        )

        stripeClients[currency].customers().update(
            customerId,
            CustomerUpdateParams
                .builder()
                .setInvoiceSettings(
                    CustomerUpdateParams.InvoiceSettings.builder()
                        .setDefaultPaymentMethod(paymentMethod.id)
                        .build(),
                )
                .build(),
        )

        return paymentMethod
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "30,null,null",
            "30,3,null",
            "100,null,null",
            "100,3,null",
            "100,null,6",
        ],
        nullValues = ["null"],
    )
    fun `generate invite coupons`(
        percentOff: Int,
        months: Int?,
        days: Int?,
    ) {
        val creator = testUser(true, "EUR05")

        every { userRepository.get(creator.id) } returns creator

        val request = Request(Method.POST, "/v1/coupons")
            .body(
                CouponInviteRequest(
                    months = months,
                    days = days,
                    percentOff = percentOff,
                    redemptions = 5,
                    count = 3,
                    campaign = "Testing campaign",
                ).toJson(),
            )

        every { userRepository.get(request) } returns creator

        val response = couponController.routePostCreateCreatorsCoupon(request)
        val body = jackson.readValue<CouponsResponseDto>(response.bodyString())

        assertEquals(3, body.data.size)
        body.data.forEach { coupon ->
            assertNotNull(coupon.id)
            assertEquals(percentOff, coupon.attributes.percentOff)
            assertEquals(months, coupon.attributes.months)
            assertEquals(days, coupon.attributes.days)
            assertEquals(creator.id, coupon.relationships.creator.id)
            assertEquals("Testing campaign", coupon.attributes.campaign)
        }
    }
}
