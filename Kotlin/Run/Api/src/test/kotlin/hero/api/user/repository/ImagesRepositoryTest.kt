package hero.api.user.repository

import io.mockk.clearAllMocks
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

internal class ImagesRepositoryTest {
    private val imageRepository: ImageRepository = ImageRepository(false)

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun dimensions() {
        val imageSize = imageRepository.dimensions("https://picsum.photos/id/0/600/400")
        assertEquals(ImageSize(600, 400), imageSize)
    }
}
