package hero.api.user.controller

import hero.api.user.repository.UsersRepository
import hero.baseutils.plusDays
import hero.jackson.fromJson
import hero.jackson.map
import hero.jwt.toJwt
import hero.model.Creator
import hero.model.ExtractedUser
import hero.model.OAuthProvider
import hero.model.Role
import hero.model.User
import hero.model.UserIdResponse
import hero.model.UserStateChange
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.unmockkAll
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.test.assertEquals

class UsersControllerFactoryTest {
    private val userRepository: UsersRepository = mockk()
    private val controller = UserFactoryController(userRepository)

    private val extractedUser: ExtractedUser = ExtractedUser(
        id = "firebase-id-123456789",
        userId = null,
        secondaryId = null,
        provider = OAuthProvider.FIREBASE,
        name = "<PERSON>",
        email = "<EMAIL>",
        imageUrl = "anne.jpg",
        accessToken = "123",
        refreshToken = "456",
        tokenExpiresAt = Instant.now().plusDays(1),
    )

    private val userId: String = "herohero-user-id"

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun testFactory() {
        val request = Request(Method.POST, "/v1/users")
            .body(extractedUser.map().toJwt())

        val slot = slot<ExtractedUser>()
        every { userRepository.factory(capture(slot)) } answers {
            User(
                id = userId,
                googleId = slot.captured.id,
                path = "test-user",
                name = "test-user",
                creator = Creator(tierId = "EUR05"),
            ) to UserStateChange.CREATED
        }
        val response = controller.routeUserFactory(request)
        assertEquals(Status.OK, response.status)
        val payload = response.bodyString().fromJson<UserIdResponse>()
        assertEquals(userId, payload.userId)
        assertEquals(Role.USER, payload.role)
    }
}
