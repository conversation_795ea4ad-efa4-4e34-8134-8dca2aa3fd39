package hero.api.statistics.controller.dto

import hero.api.post.controller.dto.examplePostResponse
import hero.api.statistics.service.AssetStats
import hero.api.statistics.service.PostCompleteStats
import hero.api.statistics.service.PostViewStats
import hero.api.statistics.service.SubscriberStatsData
import java.time.LocalDate

val exampleSubscriberDailyStatisticsResponse = SubscriberDailyStatisticsResponse(
    data = listOf(SubscriberStatsData(LocalDate.parse("2024-03-03"), 10, 9, 20, 30, 500)),
)

val examplePostWithViewStatsResponse = PostWithViewStatsResponse(examplePostResponse, PostViewStats("post-id", 10))

val exampleMostViewedPostsResponse = MostViewedPostsResponse(
    data = listOf(examplePostWithViewStatsResponse),
)

val examplePostWithCompleteStatsResponse = PostWithCompleteStatsResponse(
    examplePostResponse,
    PostCompleteStats(
        postId = "post-id",
        views = 10,
        assetStats = listOf(
            AssetStats(
                assetId = "vjsnrnfy",
                plays = 10,
                completes = 20,
                averagePlayDurationSeconds = 30.0,
            ),
        ),
    ),
)

val exampleExpectedIncomeResponse = ExpectedIncomeResponse(
    grossIncomeCents = 1000,
    netIncomeCents = 879,
)
