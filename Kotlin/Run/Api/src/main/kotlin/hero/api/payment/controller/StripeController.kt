package hero.api.payment.controller

import com.github.kittinunf.fuel.httpGet
import com.stripe.model.Charge
import com.stripe.model.SetupIntent
import com.stripe.param.AccountCreateParams
import com.stripe.param.AccountCreateParams.Type.CUSTOM
import com.stripe.param.AccountCreateParams.Type.EXPRESS
import com.stripe.param.AccountCreateParams.Type.STANDARD
import hero.api.category.repository.CategoriesRepository
import hero.api.invoice.controller.response.InvoicesDtoIncluded
import hero.api.invoice.controller.response.InvoicesDtoMeta
import hero.api.invoice.controller.response.InvoicesDtoResponse
import hero.api.invoice.controller.response.invoiceDtoExample
import hero.api.invoice.util.fetchConversionRates
import hero.api.payment.scripts.initializeStripeScript
import hero.api.payment.service.StripePaymentsService
import hero.api.post.service.fetchActiveSubscription
import hero.api.subscriber.repository.PaymentIntentStatus
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.subscriber.repository.SubscribersRepository
import hero.api.subscriber.repository.SubscribersSort
import hero.api.user.controller.UsersJsonApiController.Companion.exampleUserResponseV2
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.api.user.repository.toDto
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.firstSuccessfulResult
import hero.baseutils.log
import hero.baseutils.plusDays
import hero.baseutils.plusHours
import hero.baseutils.serviceCall
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.ServerException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.root
import hero.gcloud.where
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.parseJwtUser
import hero.http4k.config.PAGE_SIZE_MAX
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.patch
import hero.http4k.extensions.post
import hero.jackson.parseEnum
import hero.jackson.to
import hero.jwt.ACCESS_TOKEN
import hero.jwt.authorization
import hero.jwt.parseJwt
import hero.jwt.toJwt
import hero.model.CancelledByRole
import hero.model.CategoryDto
import hero.model.CouponMethod
import hero.model.Creator
import hero.model.Currency
import hero.model.Invoice
import hero.model.InvoiceDto
import hero.model.InvoiceDtoAttributes
import hero.model.InvoiceDtoRelationships
import hero.model.InvoiceItem
import hero.model.InvoiceItemType
import hero.model.ListResponseMeta
import hero.model.ModeratorPermission
import hero.model.PostDto
import hero.model.PostDtoRelationship
import hero.model.Role
import hero.model.StripeRequirements
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriberType
import hero.model.SubscriptionDto
import hero.model.SubscriptionDtoAttributes
import hero.model.SubscriptionDtoRelationships
import hero.model.SubscriptionsDtoIncluded
import hero.model.SubscriptionsDtoResponse
import hero.model.SubscriptionsDtoStatus
import hero.model.Tier
import hero.model.TierDtoRelationship
import hero.model.User
import hero.model.UserCompany
import hero.model.UserCompanyPublic
import hero.model.UserDtoRelationship
import hero.model.VatPayer
import hero.model.euCountries
import hero.model.toPublic
import hero.model.topics.CardCreateType
import hero.model.topics.RefundMethod
import hero.stripe.model.ChargeStatus
import hero.stripe.model.SetupIntentStatus
import hero.stripe.model.StripeConnectResponse
import hero.stripe.model.StripeDeclineCode
import hero.stripe.model.StripeErrorCode
import hero.stripe.model.StripeLoginResponse
import hero.stripe.service.CancelSubscriptionCommandService
import hero.stripe.service.CancelSubscriptionImmediately
import hero.stripe.service.StripeAccountService
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripePaymentMethodsService.SetupResponse
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMapping
import hero.stripe.service.nullIfNotFound
import hero.stripe.service.stripeRetry
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Body
import org.http4k.core.ContentType
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.with
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.contentType
import org.http4k.lens.nonEmptyString
import org.http4k.lens.string
import java.time.Instant
import java.time.ZoneOffset
import kotlin.concurrent.thread

class StripeController(
    private val production: Boolean,
    private val hostname: String,
    private val categoriesRepository: CategoriesRepository,
    private val stripe: StripeService,
    private val stripePublicKeys: Map<Currency, String>,
    private val stripePaymentsService: StripePaymentsService,
    private val stripePaymentMethods: StripePaymentMethodsService,
    private val stripeAccountService: StripeAccountService,
    private val subscriptionService: StripeSubscriptionService,
    private val subscriberRepository: SubscribersRepository,
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val tierRepository: TiersRepository,
    private val userRepository: UsersRepository,
    private val invoicesCollection: TypedCollectionReference<Invoice>,
    private val countryToVatMapping: VatMapping,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
    private val cancelSubscriptionCommandService: CancelSubscriptionCommandService,
) {
    data class StripeAuth(
        val publishableKey: String,
    )

    @Suppress("unused")
    val routeStripeAuth: ContractRoute =
        "/v1/stripe/publishable-key".get(
            summary = "Get metadata for subscription init.",
            parameters = object {
                val authorization = Header.authorization()
                val currencyQuery = Query.enum<Currency>()
                    .defaulted("currency", Currency.EUR, "Currency to decide which Stripe account to handle.")
            },
            responses = listOf(
                Status.OK example StripeAuth("pk_123456789"),
            ),
            tag = "Stripe",
            handler = { request, parameters ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val currency = parameters.currencyQuery(request)
                Response(Status.OK)
                    .body(StripeAuth(stripePublicKeys[currency]!!))
            },
        )

    @Suppress("unused")
    val routeStripeConnect: ContractRoute =
        "/v1/stripe/connection-links".post(
            summary = "Connects user to Stripe.",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = null,
            tag = "Stripe",
            responses = listOf(
                Status.OK example StripeConnectResponse(
                    url = "https://stripe/connect",
                    expires = Instant.now().plusDays(1),
                ),
            ),
            handler = { request, _ ->
                val user = userRepository.get(request)
                val currency = Tier.ofId(user.creator.tierId).currency
                if (user.creator.stripeAccountId == null) {
                    val accountId = stripeAccountService.createAccount(user).id
                    userRepository.collection[user.id].field(
                        root(User::creator).path(Creator::stripeAccountId),
                    ).update(accountId)
                    user.creator.stripeAccountId = accountId
                }
                val accountLink = stripeAccountService.createAccountLink(
                    user.id,
                    user.creator.stripeAccountId!!,
                    mapOf("userId" to user.id).toJwt(),
                    currency,
                )
                Response(Status.OK).body(StripeConnectResponse.of(accountLink))
            },
        )

    private fun connectResponse(status: Status): String =
        """
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8"/>
          <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
          <meta name="referrer" content="no-referrer"/>
          <title>Herohero - Stripe connect</title>
          <script>
            if (!window.opener || ['FB', 'Instagram', 'Snapchat'].find(inAppBrowser => navigator.userAgent.includes(inAppBrowser))) {
              window.location.href = '$hostname/?goto=payouts&status=${status.code}';
            } else {
              window.opener.postMessage({ status: ${status.code} }, '${if (production) hostname else "*"}');
            }
          </script>
        </head>
        <body>
          Stripe connected. Feel free to close this window.
        </body>
        </html>
        """.trimIndent()

    data class StripeCallback(
        val userId: String,
    )

    @Suppress("unused")
    val routeStripeReturn: ContractRoute =
        "/v1/stripe/return".get(
            summary = "Return Stripe callback.",
            parameters = object {
                val token = Query.string().required("token", "Stripe response user identification.")
            },
            responses = listOf(
                Status.OK,
                Status.UNPROCESSABLE_ENTITY,
                Status.INTERNAL_SERVER_ERROR,
            ).map { it example connectResponse(it) },
            tag = "Stripe",
            handler = { request, parameters ->
                var requirements: StripeRequirements? = null
                val connectionStatus = try {
                    val stripeCallback = parameters.token(request).parseJwt().to<StripeCallback>()
                    val user = userRepository.get(stripeCallback.userId)
                    val currency = Tier.ofId(user.creator.tierId).currency
                    // once the user is onboarded, they should use dashboard-link instead of connection-link - this is to distinguish the difference
                    user.creator.stripeAccountOnboarded = stripe.onboardingFinished(user, currency)
                    var valid = false
                    // There is no additional polling on FE as stated here:
                    // https://herohero-workspace.slack.com/archives/D01B8LL4KPV/p1644322142329669?thread_ts=**********.872679&cid=D01B8LL4KPV
                    for (i in 1..5) {
                        requirements = stripe.requirements(user, "callback", currency)
                        valid = requirements.valid
                        if (valid) {
                            break
                        } else {
                            log.info(
                                "Stripe account of ${user.id} still not valid ($i/5), waiting 1000 ms.",
                                mapOf("userId" to user.id),
                            )
                            Thread.sleep(1000)
                            requirements = stripe.requirements(user, "callback-retry", currency)
                            valid = requirements.valid
                        }
                    }
                    user.creator.stripeAccountActive = valid
                    user.creator.stripeRequirements = requirements
                    if (user.verifiedAt == null && user.creator.verified) {
                        userRepository.store(user.copy(verifiedAt = Instant.now()))
                    } else {
                        userRepository.store(user)
                    }
                    if (valid) Status.OK else Status.UNPROCESSABLE_ENTITY
                } catch (e: Exception) {
                    log.fatal("Error when processing stripe connection return callback.", cause = e)
                    Status.INTERNAL_SERVER_ERROR
                }
                Response(connectionStatus)
                    // see https://gitlab.com/heroheroco/frontend/-/issues/341
                    .header("Referrer-Policy", "no-referrer")
                    .with(Body.string(ContentType.TEXT_HTML).toLens() of connectResponse(connectionStatus))
            },
        )

    @Suppress("unused")
    val routeStripeDashboard: ContractRoute =
        "/v1/stripe/dashboard-links".post(
            summary = "Get login link to user's Stripe dashboard.",
            parameters = object {},
            responses = listOf(
                Status.OK example StripeLoginResponse("https://stripe/login", EXPRESS),
            ),
            tag = "Stripe",
            receiving = null,
            handler = { request, _ ->
                val user = userRepository.get(request)
                if (user.creator.stripeAccountId == null) {
                    throw BadRequestException(
                        "User has not yet connnected their Stripe account.",
                        mapOf("userId" to user.id),
                    )
                }
                val currency = Tier.ofId(user.creator.tierId).currency
                val account = stripe.getAccount(user.creator.stripeAccountId!!, currency)
                val accountType = AccountCreateParams.Type.valueOf(account.type.uppercase())
                val loginLink = when (accountType) {
                    EXPRESS -> try {
                        stripe.getLoginLink(user.creator.stripeAccountId!!, currency).url
                    } catch (e: Exception) {
                        throw IllegalStateException("Cannot create dashboard link for ${user.id}", e)
                    }

                    STANDARD,
                    CUSTOM,
                    -> "https://dashboard.stripe.com"
                }
                Response(Status.OK).body(StripeLoginResponse(loginLink, accountType))
            },
        )

    @Suppress("unused")
    @Deprecated("Use V2.")
    val routeDeletePaymentMethodV1: ContractRoute =
        ("/v1/payment-methods" / Path.string().of("paymentMethodId")).delete(
            summary = "Delete stored payment method.",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            meta = {
                it.markAsDeprecated()
            },
            tag = "Stripe",
            handler = { request, _, paymentMethodId ->
                val user = userRepository.get(request)
                if (!user.isStripeCustomer) {
                    throw NotFoundException("User does not have stripeCustomerId yet.", mapOf("userId" to user.id))
                }
                user.customerIds.entries.firstSuccessfulResult { (currency, customerId) ->
                    stripePaymentMethods.deletePaymentMethodsWithSameSuffix(
                        customerId = customerId,
                        paymentMethodIdToDelete = paymentMethodId,
                        currency = Currency.valueOf(currency),
                    )
                }.onFailure {
                    throw it
                }

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeDeletePaymentMethod: ContractRoute =
        ("/v2/payment-methods" / Path.string().of("paymentMethodId")).delete(
            summary = "Delete stored payment method.",
            parameters = object {
                val authorization = Header.authorization()
                val currency = Query.enum<Currency>().required("currency", "Currency for which delete this card.")
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            tag = "Stripe",
            handler = { request, parameters, paymentMethodId ->
                val user = userRepository.get(request)
                val currency = parameters.currency(request)
                val customerId = user.customerIds[currency.name]
                    ?: throw NotFoundException(
                        "Customer for ${user.id}/$currency was not found.",
                        mapOf("userId" to user.id),
                    )
                stripePaymentMethods.deletePaymentMethodsWithSameSuffix(
                    customerId = customerId,
                    paymentMethodIdToDelete = paymentMethodId,
                    currency = currency,
                )
                Response(Status.NO_CONTENT)
            },
        )

    data class PaymentMethodPostBody(
        val id: String,
        val currency: Currency,
        val makeDefault: Boolean,
        val cardCreateType: CardCreateType? = null,
    )

    @Suppress("unused")
    val routePostPaymentMethod: ContractRoute =
        "/v3/payment-methods".post(
            summary = "Stores payment method.",
            parameters = object {
                val authorization = Header.authorization()
            },
            tag = "Stripe",
            responses = listOf(
                Status.OK example SetupResponse(
                    paymentMethodId = "pm_aaaaaaaaa",
                    customerId = "cu_bbbbbbbbb",
                    status = SetupIntentStatus.SUCCEEDED,
                    clientSecret = "abcd",
                    nextAction = null,
                    declineCode = null,
                    declineError = null,
                    declineMessage = null,
                ),
                Status.ACCEPTED example SetupResponse(
                    paymentMethodId = "pm_aaaaaaaaa",
                    customerId = "cu_bbbbbbbbb",
                    status = SetupIntentStatus.REQUIRES_ACTION,
                    clientSecret = "abcd",
                    nextAction = SetupIntent.NextAction().also {
                        it.type = "use_stripe_sdk"
                        it.useStripeSdk = mapOf(
                            "type" to "three_d_secure_redirect",
                            "stripe_js" to "https://hooks.stripe.com/redirect/authenticate/src_1Mv…",
                            "source" to "src_1Mv",
                        )
                    },
                    declineCode = null,
                    declineError = null,
                    declineMessage = null,
                ),
                Status.UNPROCESSABLE_ENTITY example SetupResponse(
                    paymentMethodId = "pm_aaaaaaaaa",
                    customerId = "cu_bbbbbbbbb",
                    clientSecret = null,
                    status = SetupIntentStatus.CANCELLED,
                    nextAction = null,
                    declineCode = StripeDeclineCode.INCORRECT_CVC,
                    declineError = StripeErrorCode.CARD_DECLINED,
                    declineMessage = null,
                ),
            ),
            receiving = PaymentMethodPostBody(
                id = "pm_123456798",
                currency = Currency.EUR,
                cardCreateType = CardCreateType.CARD,
                makeDefault = true,
            ),
            handler = { request, _ ->
                val user = userRepository.get(request)
                val requestBody = lens<PaymentMethodPostBody>(request)
                val customerId = subscriberStripeRepository.customerFactory(user.id, requestBody.currency)
                log.info(
                    "User ${user.id}/$customerId is adding a payment method " +
                        "${requestBody.cardCreateType}/${requestBody.currency}/${requestBody.id}",
                    mapOf("userId" to user.id, "paymentMethodId" to requestBody.id),
                )
                val setupResponse = stripePaymentMethods
                    .putPaymentMethodViaSetupIntent(
                        requestBody.id,
                        customerId,
                        requestBody.currency,
                        requestBody.makeDefault,
                        requestBody.cardCreateType,
                    )

                Response(
                    when (setupResponse.status) {
                        SetupIntentStatus.SUCCEEDED,
                        -> Status.OK

                        SetupIntentStatus.REQUIRES_ACTION,
                        SetupIntentStatus.REQUIRES_CONFIRMATION,
                        -> Status.ACCEPTED

                        else
                        -> Status.UNPROCESSABLE_ENTITY
                    },
                ).body(setupResponse)
            },
        )

    data class StripePaymentMethod(
        val id: String,
        val created: Instant,
        val lastFour: String,
        val brand: String,
        val currency: Currency,
        val isDefault: Boolean,
        val expiryYear: Long,
        val expiryMonth: Long,
    )

    data class PaymentMethodResponse(
        val list: List<StripePaymentMethod>,
    )

    @Suppress("unused")
    val routeGetStripePaymentMethods: ContractRoute =
        "/v1/payment-methods".get(
            summary = "List user's payment methods.",
            parameters = object {
                val authorization = Header.authorization()
            },
            tag = "Stripe",
            responses = listOf(
                Status.OK example PaymentMethodResponse(
                    listOf(
                        StripePaymentMethod(
                            id = "pm_123456789",
                            created = Instant.now(),
                            lastFour = "1234",
                            brand = "Visa",
                            currency = Currency.EUR,
                            isDefault = true,
                            expiryYear = 2020L,
                            expiryMonth = 10L,
                        ),
                    ),
                ),
            ),
            handler = { request, _ ->
                val user = userRepository.get(request)
                val paymentMethods = if (!user.isStripeCustomer) {
                    listOf()
                } else {
                    user.customerIds.entries.map { (currencyString, stripeCustomerId) ->
                        val currency = Currency.valueOf(currencyString)
                        val customer = stripe.customer(stripeCustomerId, currency)

                        val paymentMethodsIterable = stripeRetry {
                            stripePaymentMethods.paymentMethods(stripeCustomerId, currency)
                        }
                        paymentMethodsIterable
                            // https://linear.app/herohero/issue/HH-3191/do-not-show-applegoogle-pay-cards-in-stored-cards
                            .filter {
                                it.metadata["cardCreateType"] !in
                                    setOf(CardCreateType.APPLE_PAY.name, CardCreateType.GOOGLE_PAY.name)
                            }
                            .distinctBy { paymentMethod -> paymentMethod.card.last4 + paymentMethod.card.brand }
                            .map { paymentMethod ->
                                StripePaymentMethod(
                                    id = paymentMethod.id,
                                    created = Instant.ofEpochSecond(paymentMethod.created),
                                    lastFour = paymentMethod.card.last4,
                                    brand = paymentMethod.card.brand,
                                    currency = currency,
                                    isDefault = customer.invoiceSettings.defaultPaymentMethod == paymentMethod.id,
                                    expiryYear = paymentMethod.card.expYear,
                                    expiryMonth = paymentMethod.card.expMonth,
                                )
                            }
                    }.flatten()
                }
                Response(Status.OK)
                    .body(PaymentMethodResponse(paymentMethods))
            },
        )

    private val subscriberExampleResponse: SubscriptionDto = SubscriptionDto(
        id = "and-mul-dkdjdj-and-mul-dkdjdj",
        attributes = SubscriptionDtoAttributes(
            status = SubscriptionsDtoStatus.INACTIVE,
            subscribedAt = Instant.now(),
            cancelAtPeriodEnd = false,
            expires = Instant.now().plusHours(5),
            type = SubscriberType.STRIPE,
            couponAppliedForMonths = 3L,
            couponAppliedForDays = null,
            couponMethod = CouponMethod.VOUCHER,
            couponPercentOff = 30L,
            couponExpiresAt = Instant.now().plusDays(5),
        ),
        relationships = SubscriptionDtoRelationships(
            user = UserDtoRelationship("and-mul-dkdjdj"),
            creator = UserDtoRelationship("mul-and-f29fca0"),
            tier = TierDtoRelationship("EUR05"),
        ),
    )

    private val subscriptionsExampleResponse: SubscriptionsDtoResponse = SubscriptionsDtoResponse(
        meta = ListResponseMeta(0, true),
        subscriptions = listOf(subscriberExampleResponse),
        included = SubscriptionsDtoIncluded(
            users = listOf(),
            tiers = listOf(),
        ),
    )

    private val subscriberExample: Subscriber = Subscriber(
        userId = "user",
        creatorId = "creator",
        tierId = "EUR05",
        status = SubscriberStatus.ACTIVE,
        cancelAtPeriodEnd = false,
        subscribed = Instant.now(),
        expires = Instant.now().plusDays(30),
        cancelledAt = Instant.now().plusDays(30),
        cancelledBy = "admin-user",
        cancelledByRole = CancelledByRole.MODERATOR,
        subscriberType = SubscriberType.STRIPE,
        refunded = false,
        refused = false,
        cancelledReason = "Insufficient funds.",
        couponId = "ABCDEFGH",
        couponMethod = CouponMethod.VOUCHER,
        couponPercentOff = 30L,
    )

    @Suppress("unused")
    val routeCreateSubscription: ContractRoute =
        (
            "/v1/users" / Path.userId().of("userId", "User who subscribes.") / "subscriptions" /
                Path.of("creatorId", "Creator to subscribe.") / "payments"
        ).post(
            summary = "Create subscription.",
            parameters = object {
                val authorization = Header.authorization()
                val isResubscription =
                    Query.boolean().defaulted("isResubscription", false, "Is the subscription resubscription")
                val tierId = Query.nonEmptyString().optional("tierId", "Tier id")
            },
            receiving = CreateSubscriptionPostBody(
                paymentMethodId = "pay_EtGYv3abB0",
                couponId = "KDJFHSL",
                cardCreateType = CardCreateType.APPLE_PAY,
            ),
            tag = "Subscriptions",
            responses = listOf(
                Status.OK example PaymentResponse(
                    PaymentResponseAttributes(
                        status = PaymentIntentStatus.SUCCEEDED,
                        subscriptionStatus = SubscriberStatus.ACTIVE,
                        createdAt = Instant.now(),
                        paymentIntentClientSecret = null,
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship("jonas-novy"),
                        post = null,
                        creator = UserDtoRelationship("adela-plechata"),
                    ),
                ),
                Status.ACCEPTED example PaymentResponse(
                    PaymentResponseAttributes(
                        status = PaymentIntentStatus.REQUIRES_ACTION,
                        subscriptionStatus = SubscriberStatus.INCOMPLETE,
                        createdAt = Instant.now(),
                        paymentIntentClientSecret = "pi_ldnwhjnsid",
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship("jonas-novy"),
                        post = null,
                        creator = UserDtoRelationship("adela-plechata"),
                    ),
                ),
                Status.UNPROCESSABLE_ENTITY example PaymentResponse(
                    PaymentResponseAttributes(
                        status = PaymentIntentStatus.CANCELLED,
                        subscriptionStatus = SubscriberStatus.CANCELLED,
                        createdAt = Instant.now(),
                        paymentIntentClientSecret = null,
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship("jonas-novy"),
                        post = null,
                        creator = UserDtoRelationship("adela-plechata"),
                    ),
                ),
            ),
            handler = { request, parameters, userId, _, creatorId, _ ->
                val user = userRepository.get(request, userId)
                val body = lens<CreateSubscriptionPostBody>()[request]
                val creator = userRepository.get(creatorId)
                val isResubscription = parameters.isResubscription(request)
                val tierId = parameters.tierId(request)
                if (!isResubscription && tierId != null) {
                    throw BadRequestException("Tier id cannot be set if not resubscribing")
                }

                if (body.paymentMethodId != null && !creator.creator.active) {
                    throw BadRequestException(
                        "Creator ${creator.id} has not finished their Stripe account pairing.",
                        labels = mapOf("creatorId" to creator.id),
                    )
                }

                if (creator.id == user.id) {
                    throw BadRequestException(
                        "User ${user.id} cannot subscribe themselves.",
                        labels = mapOf("userId" to user.id),
                    )
                }

                val paymentResponse = if (isResubscription) {
                    if (tierId == null) {
                        throw BadRequestException("Tier id cannot be null when resubscribing")
                    }
                    if (body.paymentMethodId == null) {
                        throw BadRequestException("Payment method must be given when resubscribing")
                    }
                    subscriberStripeRepository.resubscribe(
                        user = user,
                        creator = creator,
                        paymentMethodId = body.paymentMethodId,
                        tierId = tierId,
                        body.cardCreateType,
                    )
                } else {
                    subscriberStripeRepository.subscribe(
                        user = user,
                        creator = creator,
                        paymentMethodId = body.paymentMethodId,
                        // uppercase/trim clean-up should be performed on FE, so just to be absolutely sure
                        couponId = body.couponId?.uppercase()?.trim(),
                        body.cardCreateType,
                    )
                }

                val status = when {
                    paymentResponse.attributes.status == PaymentIntentStatus.SUCCEEDED -> Status.OK
                    paymentResponse.attributes.status == PaymentIntentStatus.REQUIRES_ACTION -> Status.ACCEPTED
                    else -> Status.UNPROCESSABLE_ENTITY
                }

                log.info(
                    "Subscription status of ${user.id} -> ${creator.id} changed: $paymentResponse",
                    mapOf("userId" to user.id, "creatorId" to creator.id),
                )

                Response(status).body(paymentResponse)
            },
        )

    data class SubscriptionPatchBody(
        val cancelAtPeriodEnd: Boolean,
    )

    @Suppress("unused")
    val routePatchSubscription: ContractRoute =
        (
            "/v1/users" / Path.userId().of("userId", "User who subscribes.") / "subscriptions" /
                Path.userId().of("creatorId", "Creator to unsubscribe.")
        ).patch(
            summary = "Modify subscription state.",
            parameters = object {
                val authorization = Header.authorization()
            },
            tag = "Subscriptions",
            responses = listOf(
                Status.OK example subscriberExample,
            ),
            receiving = SubscriptionPatchBody(true),
            handler = { request, _, userId, _, creatorId ->
                val user = userRepository.get(request, userId)
                val creator = userRepository.get(creatorId)
                val subscriber = subscriberRepository.subscribedTo(userId, creatorId)
                    .first
                    .firstOrNull()
                    ?: throw BadRequestException(
                        "User $userId does not subscribe $creatorId yet.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )

                if (subscriber.subscriberType == SubscriberType.STRIPE && !user.isStripeCustomer) {
                    throw BadRequestException("User is not yet Stripe customer.", mapOf("userId" to user.id))
                }

                val cancelAtPeriodEnd = lens<SubscriptionPatchBody>()(request).cancelAtPeriodEnd

                if (subscriber.cancelAtPeriodEnd == cancelAtPeriodEnd) {
                    // no action needed
                    return@patch Response(Status.OK)
                        .body(subscriber)
                }

                @Suppress("ktlint:standard:max-line-length")
                log.info(
                    "User is ${if (cancelAtPeriodEnd) "cancelling subscription at period end" else "restoring its subscription"}.",
                    mapOf(
                        "userId" to user.id,
                        "creatorId" to creator.id,
                        "subscriberType" to subscriber.subscriberType,
                    ),
                )

                val patchedSubscriber = subscriberStripeRepository.patchSubscription(
                    user,
                    creator,
                    subscriber,
                    cancelAtPeriodEnd,
                )

                Response(Status.OK)
                    .body(patchedSubscriber)
            },
        )

    @Suppress("unused")
    val routeDeleteSubscription: ContractRoute =
        (
            "/v1/users" / Path.userId().of("userId", "User who subscribes.") / "subscriptions" /
                Path.userId().of("creatorId", "Creator to unsubscribe.")
        ).delete(
            summary = "Immediately cancels user's subscription. Currently only Stripe subscriptions in `past_due`" +
                " state are supported.",
            parameters = object {
                val authorization = Header.authorization()
                val refundMethod = Query.enum<RefundMethod>().optional(
                    "refundMethod",
                    refundMethodDescription,
                )
            },
            tag = "Subscriptions",
            responses = listOf(
                Status.OK example subscriberExample,
            ),
            handler = { request, parameters, userId, _, creatorId ->
                val jwtUser = request.parseJwtUser(allowImpersonation = false) ?: throw UnauthorizedException()
                val user = userRepository.get(userId)
                val creator = userRepository.get(creatorId)
                val currency = Tier.ofId(creator.creator.tierId).currency
                val refundMethod = parameters.refundMethod(request)

                val isModerator = jwtUser.roleIndex == Role.MODERATOR.ordinal
                val isUser = jwtUser.id == userId
                val isCreator = jwtUser.id == creatorId

                val subscriber = subscribersCollection.fetchActiveSubscription(userId, creatorId)
                    ?: throw BadRequestException(
                        "User $userId does not subscribe $creatorId yet.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )

                // There are more cases:
                // 1. Admin deleting any subscription
                // 2. Creator deleting an unwanted follower
                // 3. User cancelling their subscription in PAST_DUE
                // 4. User cancelling their free subscription

                val cancelledByRole = when {
                    isModerator -> {
                        // moderator can always delete his own subscriber, no matter his permissions
                        val canCancelSubscribers = ModeratorPermission.canCancelSubscribers(user.moderatorPermissions)
                        if (subscriber.creatorId != jwtUser.id && !canCancelSubscribers) {
                            throw ForbiddenException()
                        }
                        if (refundMethod == null) {
                            throw BadRequestException(
                                "Moderator ${jwtUser.id} must provide refundMethod.",
                                mapOf("userId" to jwtUser.id),
                            )
                        }
                        CancelledByRole.MODERATOR
                    }

                    isCreator -> {
                        if (refundMethod != null) {
                            throw BadRequestException(
                                "Creator ${jwtUser.id} may never change refundMethod.",
                                mapOf("userId" to jwtUser.id),
                            )
                        }
                        CancelledByRole.CREATOR
                    }

                    isUser -> {
                        if (refundMethod != null) {
                            throw BadRequestException(
                                "User ${jwtUser.id} may never change refundMethod.",
                                mapOf("userId" to jwtUser.id),
                            )
                        }
                        CancelledByRole.USER
                    }

                    else ->
                        @Suppress("ktlint:standard:max-line-length")
                        throw ForbiddenException(
                            "JwtUser ${jwtUser.id} does not correspond neither to user $userId, nor creator $creatorId, nor Moderator.",
                            mapOf("userId" to jwtUser.id),
                        )
                }

                log.info(
                    "User ${jwtUser.id} is cancelling subscription ${subscriber.id}.",
                    mapOf(
                        "userId" to user.id,
                        "creatorId" to creator.id,
                        "subscriberType" to subscriber.subscriberType,
                    ),
                )

                val stripeSubscription = subscriptionService.getSubscriptionsByCustomer(
                    customerId = user.customerId(creator.creator) ?: throw ConflictException(
                        "User $userId is not yet Stripe customer.",
                        mapOf("userId" to user.id),
                    ),
                    metaCreatorId = creatorId,
                    filterActive = true,
                    currency = currency,
                ).firstOrNull()
                    ?: throw NotFoundException(
                        "No Stripe subscription was found for user ${user.id} and creator $creatorId.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )

                val resultRefundMethod = when (cancelledByRole) {
                    // if creator rejects user, the refund will be provided
                    CancelledByRole.CREATOR -> RefundMethod.REFUND
                    // no refund will be provided if user request immediate cancellation
                    CancelledByRole.USER -> RefundMethod.NONE
                    // no refund will be provided if user request immediate cancellation
                    CancelledByRole.MODERATOR -> refundMethod!!
                }
                val subscription = cancelSubscriptionCommandService.execute(
                    CancelSubscriptionImmediately(
                        subscriptionId = stripeSubscription.id,
                        cancelledBy = jwtUser.id,
                        cancelledByRole = cancelledByRole,
                        refundMethod = resultRefundMethod,
                        currency = currency,
                    ),
                )

                // we failed to cancel the subscription
                if (SubscriberStatus.of(subscription.status).isActive) {
                    error("Failed to cancel user's ${user.id} subscription to creator ${creator.id}")
                }
                subscribersCollection[subscriber.id].field(Subscriber::status).update(SubscriberStatus.CANCELLED)

                Response(Status.OK).body(subscriber.copy(status = SubscriberStatus.CANCELLED))
            },
        )

    private fun Subscriber.toSubscriptionsDto(details: Boolean): SubscriptionDto =
        SubscriptionDto(
            id = id,
            attributes = SubscriptionDtoAttributes(
                subscribedAt = subscribed,
                status = when {
                    !details -> null
                    status == SubscriberStatus.PAST_DUE -> SubscriptionsDtoStatus.PAST_DUE
                    status.isActive -> SubscriptionsDtoStatus.ACTIVE
                    else -> SubscriptionsDtoStatus.INACTIVE
                },
                cancelAtPeriodEnd = if (details) cancelAtPeriodEnd else null,
                expires = if (details) expires else null,
                type = if (details) subscriberType else null,
                couponAppliedForMonths = if (details) couponAppliedForMonths else null,
                couponAppliedForDays = if (details) couponAppliedForDays else null,
                couponMethod = if (details) couponMethod else null,
                couponPercentOff = if (details) couponPercentOff else null,
                couponExpiresAt = if (details) couponExpiresAt else null,
            ),
            relationships = SubscriptionDtoRelationships(
                user = UserDtoRelationship(userId),
                creator = UserDtoRelationship(creatorId),
                tier = if (details) TierDtoRelationship(tierId) else null,
            ),
        )

    enum class SubscriptionOf { USER, CREATOR }

    @Suppress("unused")
    val routeGetSubscriptions: ContractRoute =
        (
            "/v2/users" / Path.userId()
                .of("userId", "User or creator depending on the ?of parameter.") / "subscriptions"
        ).get(
            summary = "List subscriptions for given user.",
            tag = "Subscriptions",
            parameters = object {
                val authorization = Header.authorization()
                val of = Query.enum<SubscriptionOf>().required("of", "Get subscription of user's/creator's side.")
                val include = Query.string()
                    .defaulted(
                        "include",
                        "",
                        "Requests inclusion of related entities. Options: [user, categories].",
                    )
                val sorting = Query.enum<SubscribersSort>()
                    .defaulted(
                        "sorting",
                        SubscribersSort.NEWEST,
                        "Defines sorting of subscribers. Possible values: $subscriberSorts, default is $newest.",
                    )
                val pageIndex = QueryUtils.pageIndex()
                val pageSize = QueryUtils.pageSize()
                val expired = Query.boolean()
                    .defaulted("expired", false, "List expired subscriptions. Default to `false`.")
            },
            responses = listOf(Status.OK to subscriptionsExampleResponse),
            handler = { request, parameters, pathUserId, _ ->
                val requester = request.getJwtUser()
                val sorting = parameters.sorting(request)
                val pageIndex = parameters.pageIndex(request)
                val pageSize = parameters.pageSize(request)
                if (pageSize > PAGE_SIZE_MAX) {
                    throw BadRequestException("Parameter pageSize is out of bounds.", mapOf())
                }
                val included = parameters.include(request).split(",")
                val of = parameters.of(request)
                if (pathUserId != requester.id) {
                    val subscriber = subscriberRepository
                        .getSubscriber(userId = requester.id, creatorId = pathUserId)
                        ?.takeIf { it.status.isActive }
                        ?: subscriberRepository
                            .getSubscriber(userId = pathUserId, creatorId = requester.id)
                            ?.takeIf { it.status.isActive }
                    if (subscriber == null) {
                        throw ForbiddenException("${requester.id} cannot list $pathUserId subscriptions")
                    }
                }
                // we fetch user of requested user in path (not the logged in one)
                val user = userRepository.get(pathUserId)
                val (subscribedTo, hasNext) = subscriberRepository.subscribedTo(
                    userId = if (of == SubscriptionOf.USER) user.id else null,
                    creatorId = if (of == SubscriptionOf.CREATOR) user.id else null,
                    expired = parameters.expired(request),
                    sorting = sorting,
                    limit = pageSize,
                    offset = pageIndex,
                )
                val categories = mutableListOf<CategoryDto>()
                val usersIncluded = if ("user" in included || "categories" in included) {
                    userRepository
                        .getUsers(
                            queryUserIds = subscribedTo.map { it.userId } + subscribedTo.map { it.creatorId },
                            limit = 0,
                            offset = PAGE_SIZE_MAX,
                        )
                        .map {
                            val userCategories = categoriesRepository.list(it.id)
                            categories += userCategories
                            userRepository.toDto(
                                user = it,
                                details = it.id == requester.id,
                                categories = userCategories,
                            )
                        }
                } else {
                    emptyList()
                }
                Response(Status.OK)
                    .body(
                        SubscriptionsDtoResponse(
                            meta = ListResponseMeta(pageIndex, hasNext),
                            subscriptions = subscribedTo.map {
                                it.toSubscriptionsDto(
                                    pathUserId == requester.id ||
                                        it.userId == requester.id ||
                                        it.creatorId == requester.id,
                                )
                            },
                            included = SubscriptionsDtoIncluded(
                                categories = categories,
                                users = usersIncluded,
                                tiers = subscribedTo.mapNotNull { it.tierId }.distinct().sorted()
                                    .map { tierRepository[it].toDto() },
                            ),
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routeGetSubscription: ContractRoute =
        (
            "/v2/users" / Path.userId().of("userId", "User who subscribes.") / "subscriptions" /
                Path.userId().of("creatorId", "Subscribed creator.")
        ).get(
            summary = "Get subscription details.",
            tag = "Subscriptions",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK to subscriberExampleResponse),
            handler = { request, _, userId, _, creatorId ->
                val requester = request.getJwtUser()
                val user = userRepository.get(request, userId)
                val creator = userRepository.get(creatorId)
                val (subscribedTo, _) = subscriberRepository.subscribedTo(user.id, creator.id)
                Response(Status.OK)
                    .body(
                        if (subscribedTo.isEmpty()) {
                            SubscriptionDto(
                                id = "${user.id}-${creator.id}",
                                attributes = SubscriptionDtoAttributes(
                                    subscribedAt = null,
                                    status = SubscriptionsDtoStatus.INACTIVE,
                                    cancelAtPeriodEnd = false,
                                    expires = null,
                                    type = null,
                                    couponAppliedForMonths = null,
                                    couponAppliedForDays = null,
                                    couponMethod = null,
                                    couponPercentOff = null,
                                    couponExpiresAt = null,
                                ),
                                relationships = SubscriptionDtoRelationships(
                                    user = UserDtoRelationship(user.id),
                                    creator = UserDtoRelationship(creator.id),
                                    tier = null,
                                ),
                            )
                        } else {
                            subscribedTo.first()
                                .toSubscriptionsDto(requester.id == userId || requester.id == creatorId)
                        },
                    )
            },
        )

    @Suppress("unused")
    val routeGetUserCharges: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "charges").get(
            summary = "List users receipts.",
            tag = "Invoices",
            parameters = object {
                val authorization = Header.authorization()
                val startingAfter =
                    Query.string().optional("startingAfter", "Fetch only notification after given id (excluded).")
                val pageSize = QueryUtils.pageSize(CHARGES_PAGE_SIZE, pageMax = null)
            },
            responses = listOf(
                Status.OK example InvoicesDtoResponse(
                    meta = InvoicesDtoMeta(
                        hasNext = false,
                        scrollAfter = "aaaaaaaaaaaa",
                    ),
                    invoices = listOf(
                        invoiceDtoExample,
                    ),
                    included = InvoicesDtoIncluded(users = listOf(exampleUserResponseV2)),
                ),
            ),
            handler = { request, parameters, userId, _ ->
                val user = userRepository.get(request, userId)
                if (user.customerIds.size > 1) {
                    log.fatal("User ${user.id} has more than one currency", mapOf("userId" to user.id))
                }
                val startingAfter = parameters.startingAfter(request)
                val pageSize = parameters.pageSize(request)
                val (invoices, hasNext) = if (!user.isStripeCustomer) {
                    // empty customer cannot have any receipts
                    listOf<InvoiceDto>() to false
                } else {
                    fun fetchChargesAndMap(
                        accumulated: List<InvoiceDto>,
                        startingAfter: String?,
                        pageSizeMultiplier: Long = 1L,
                    ): Pair<List<InvoiceDto>, Boolean> {
                        // TODO for now we fetch receipts only of the first existing customer
                        val firstCustomer = user.customerIds.entries.first()
                        val firstCurrency = Currency.valueOf(firstCustomer.key)
                        val (charges, hasNextPage) = stripe.charges(
                            // TODO not sure how to do it right now
                            customerId = firstCustomer.value,
                            startingAfter = startingAfter,
                            currency = firstCurrency,
                            pageLimit = pageSizeMultiplier * pageSize,
                        )
                        val renderedCharges = renderCharges(user, charges, firstCurrency)
                        val totalCharges = accumulated + renderedCharges
                        val nextPageSizeMultiplier = if (renderedCharges.isEmpty()) pageSizeMultiplier * 2 else 1

                        return if (hasNextPage && totalCharges.size < pageSize) {
                            fetchChargesAndMap(totalCharges, charges.lastOrNull()?.id, nextPageSizeMultiplier)
                        } else {
                            totalCharges.take(pageSize) to hasNextPage
                        }
                    }

                    fetchChargesAndMap(listOf(), startingAfter)
                }

                val creators = invoices.map { it.relationships.parentUser.id }
                    .distinct()
                    .map { userRepository.find(it) ?: error("Failed to find user $it") }
                    .map { userRepository.toDto(it, false, listOf()) }

                Response(Status.OK)
                    .body(
                        InvoicesDtoResponse(
                            meta = InvoicesDtoMeta(
                                hasNext = hasNext,
                                scrollAfter = if (hasNext) invoices.lastOrNull()?.id else null,
                            ),
                            invoices = invoices,
                            included = InvoicesDtoIncluded(
                                users = creators + userRepository.toDto(user, true, emptyList()),
                            ),
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routeGetUserCharge: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "charges" / Path.string().of("chargeId")).get(
            summary = "Get user receipt by its charge or payment intent id.",
            tag = "Invoices",
            parameters = object {
                val markSeen = Query.boolean()
                    .defaulted("markSeen", false, "Mark the receipt as seen by customer.")
            },
            responses = listOf(
                Status.OK example invoiceDtoExample,
            ),
            handler = { request, parameters, userId, _, chargeOrPaymentIntentId ->
                // we must use `find` method to fetch even deleted users
                val user = userRepository.find(userId)
                    ?: throw NotFoundException("User $userId was not found.")
                val markSeen = parameters.markSeen(request)

                if (!user.isStripeCustomer) {
                    throw ConflictException("User ${user.id} is not yet Stripe customer.")
                }

                val chargeId = if (chargeOrPaymentIntentId.startsWith("pi_")) {
                    nullIfNotFound { stripe.paymentIntent(chargeOrPaymentIntentId, Currency.EUR) }?.latestCharge
                        ?: nullIfNotFound { stripe.paymentIntent(chargeOrPaymentIntentId, Currency.USD) }?.latestCharge
                        ?: throw NotFoundException("PaymentIntent $chargeOrPaymentIntentId was not found.")
                } else {
                    chargeOrPaymentIntentId
                }

                val charge = nullIfNotFound { stripe.charge(chargeId, Currency.EUR) }
                    ?: nullIfNotFound { stripe.charge(chargeId, Currency.USD) }
                    ?: throw NotFoundException("Charge $chargeId was not found.")

                if (markSeen) {
                    thread {
                        stripe.markChargeAsSeen(charge)
                    }
                }

                // vouchers may not contain customerId, we need to check also metadata
                if (charge.customer !in user.customerIds.values && charge.metadata["userId"] != user.id) {
                    throw ForbiddenException(
                        "User ${user.id} cannot access charge $chargeId.",
                        mapOf("userId" to userId),
                    )
                }

                val invoices = renderCharges(user, listOf(charge), parseEnum<Currency>(charge.currency)!!)
                Response(Status.OK).body(invoices.first())
            },
        )

    data class ChargeDetails(
        val amount: Long,
        val quantity: Long,
        val description: String,
        val creatorId: String,
        val couponId: String?,
        val timestamp: Instant,
        val country: String,
    )

    private fun Charge.resolveCompany(): UserCompany? {
        val payoutId = transferObject
            ?.destinationPaymentObject
            ?.metadata
            ?.get("payoutId")
            ?: return null
        val invoice = invoicesCollection
            .where(Invoice::stripePayoutId).isEqualTo(payoutId)
            .fetchSingle()
        return invoice?.invoicedCompany
    }

    private fun renderCharges(
        user: User,
        list: List<Charge>,
        currency: Currency,
    ): List<InvoiceDto> =
        list
            .filter { parseEnum<ChargeStatus>(it.status) == ChargeStatus.SUCCEEDED }
            .mapNotNull { charge ->
                val chargeCountry = charge.paymentMethodDetails.card.country.uppercase()
                val creatorId: String
                val amountsAndQuantities: List<ChargeDetails> = when {
                    charge.invoice != null -> {
                        val stripeInvoice = stripe.invoice(
                            charge.invoice,
                            currency,
                            listOf("subscription", "payment_intent.payment_method"),
                        )
                        val invoiceItems = stripeInvoice.lines.data
                        creatorId = stripeInvoice.subscriptionObject?.metadata?.get("creatorId")
                            ?: stripeInvoice.paymentIntentObject?.metadata?.get("creatorId")
                            ?: throw IllegalStateException("Cannot get creatorId for charge ${charge.id}.")
                        invoiceItems.map {
                            ChargeDetails(
                                amount = it.amount,
                                quantity = it.quantity,
                                description = it.description,
                                creatorId = creatorId,
                                couponId = null,
                                timestamp = Instant.ofEpochSecond(stripeInvoice.created),
                                country = chargeCountry,
                            )
                        }
                    }

                    charge.metadata["creatorId"] != null -> {
                        creatorId = charge.metadata["creatorId"]
                            ?: error("Metadata `creatorId` was missing for charge ${charge.id}.")
                        listOf(
                            ChargeDetails(
                                amount = charge.amount,
                                quantity = 1,
                                description = charge.description ?: "Missing description",
                                creatorId = creatorId,
                                couponId = charge.metadata["couponId"],
                                timestamp = Instant.ofEpochSecond(charge.created),
                                country = chargeCountry,
                            ),
                        )
                    }

                    else -> {
                        log.fatal(
                            "Charge ${charge.id} cannot have receipt as it is missing " +
                                "both invoice and creatorId in metadata.",
                        )
                        return@mapNotNull null
                    }
                }

                // here we need to find also deleted creators, therefore using `find` instead of `get`
                val creator = userRepository.find(creatorId) ?: throw NotFoundException("Creator $creatorId not found")
                val creatorCountry = creator.company?.country ?: "CZ"

                val created = Instant.ofEpochSecond(charge.created)

                // if creator has enabled one-stop-shop, we have to display VAT of the destination country
                val receiptCountry = if (creator.oneStopShopAt != null && creator.oneStopShopAt!! > created)
                    chargeCountry
                else
                    creatorCountry

                val items = amountsAndQuantities.map { item ->
                    // spec: https://linear.app/herohero/issue/HH-3635/oss-for-creators
                    val countryForTaxes = if (creator.oneStopShopAt != null && creator.oneStopShopAt!! < item.timestamp)
                        item.country
                    else
                        creatorCountry

                    InvoiceItem(
                        title = item
                            .description
                            .replace("\\s*\\d+\\s*×\\s*".toRegex(), "")
                            .replace("\\(at.*?\\)$".toRegex(), "")
                            .replace("\\s*[/]\\s*[A-Z]+\\s*\\d+ subscription".toRegex(), " – subscription"),
                        priceUnit = item.amount.toInt() / item.quantity.toInt(),
                        priceUnit4D = item.amount.toInt() * 100,
                        count = item.quantity.toInt(),
                        priceTotal = item.amount.toInt(),
                        priceTotal4D = item.amount.toInt() * 100,
                        type = InvoiceItemType.CHARGE,
                        vatCents = if (creator.company?.vatType == VatPayer.VAT_PAYER && receiptCountry in euCountries)
                            countryToVatMapping[countryForTaxes, item.timestamp]
                        else
                            0,
                        comparableToPayout = true,
                        metadata = mapOf("couponId" to item.couponId, "creatorId" to item.creatorId),
                    )
                }
                val conversionRates = fetchConversionRates(created.atZone(ZoneOffset.UTC).toLocalDate())

                InvoiceDto(
                    id = charge.id,
                    attributes = InvoiceDtoAttributes(
                        stripeAccountId = null,
                        stripePayoutId = null,
                        timestamp = created,
                        total = charge.amount,
                        total4D = charge.amount * 100,
                        currency = currency,
                        currencyPayout = currency,
                        privateItems = items,
                        issuingCompany = creator.company?.toPublic() ?: UserCompanyPublic(namePublic = creator.name),
                        invoicedCompany = charge.resolveCompany()?.toPublic()
                            ?: user.company?.toPublic()
                            ?: UserCompanyPublic(namePublic = user.name),
                        eurConversionRateCents = if (
                            creatorCountry == "CZ" &&
                            charge.currency == Currency.EUR.name.lowercase()
                        )
                            conversionRates[Currency.CZK]!!.movePointRight(2).toLong()
                        else
                            null,
                        sheetReportLink = null,
                        euReverseCharged = false,
                        cardLast4 = charge.paymentIntentObject?.paymentMethodObject?.card?.last4,
                        authToken = "zxcvbnmqwertyu",
                    ),
                    relationships = InvoiceDtoRelationships(
                        user = UserDtoRelationship(user.id),
                        parentUser = UserDtoRelationship(creatorId),
                    ),
                )
            }

    @Suppress("unused")
    val routePostUnlockRequest: ContractRoute =
        "/v1/post-unlock-request".post(
            summary = "Unlock paid post.",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = PostUnlockRequest(
                postId = "direct-message-123",
                paymentMethodId = "pay_EtGYv3abB0",
            ),
            tag = "Subscriptions",
            responses = listOf(
                Status.OK example PaymentResponse(
                    PaymentResponseAttributes(
                        status = PaymentIntentStatus.SUCCEEDED,
                        subscriptionStatus = null,
                        createdAt = Instant.now(),
                        paymentIntentClientSecret = "secret-secret",
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship("user-user"),
                        post = PostDtoRelationship("post-post"),
                        creator = UserDtoRelationship("creator-creator"),
                    ),
                ),
                Status.ACCEPTED example PaymentResponse(
                    PaymentResponseAttributes(
                        status = PaymentIntentStatus.REQUIRES_ACTION,
                        subscriptionStatus = null,
                        createdAt = Instant.now(),
                        paymentIntentClientSecret = "secret-secret",
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship("user-user"),
                        post = PostDtoRelationship("post-post"),
                        creator = UserDtoRelationship("creator-creator"),
                    ),
                ),
                Status.UNPROCESSABLE_ENTITY example PaymentResponse(
                    PaymentResponseAttributes(
                        status = PaymentIntentStatus.CANCELLED,
                        subscriptionStatus = null,
                        createdAt = Instant.now(),
                        paymentIntentClientSecret = "secret-secret",
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship("user-user"),
                        post = PostDtoRelationship("post-post"),
                        creator = UserDtoRelationship("creator-creator"),
                    ),
                ),
            ),
            handler = { request, _ ->
                val user = userRepository.get(request)
                val body = lens<PostUnlockRequest>()[request]
                val post = serviceCall("api", "/v2/posts/${body.postId}")
                    .httpGet()
                    .header("Cookie", "$ACCESS_TOKEN=${user.id.authorization()}")
                    .fetch<PostDto>()

                val creator = userRepository.get(post.relationships.user!!.id)
                if (!creator.creator.active) {
                    throw ConflictException(
                        "User ${creator.id} is not active creator.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )
                }
                if ((post.attributes.price ?: 0L) == 0L) {
                    throw ConflictException(
                        "Post ${post.id} is not paid.",
                        mapOf("userId" to user.id, "creatorId" to creator.id, "postId" to post.id),
                    )
                }

                if (stripePaymentsService.isUnlocked(post, user)) {
                    // this post is already paid
                    return@post Response(Status.OK)
                        .body(
                            PaymentResponse(
                                PaymentResponseAttributes(
                                    status = PaymentIntentStatus.SUCCEEDED,
                                    subscriptionStatus = null,
                                    createdAt = null,
                                    paymentIntentClientSecret = null,
                                    couponCode = null,
                                ),
                                PaymentResponseRelationships(
                                    user = UserDtoRelationship(user.id),
                                    post = PostDtoRelationship(post.id!!),
                                    creator = post.relationships.user!!,
                                ),
                            ),
                        )
                }

                val paymentIntent = stripePaymentsService
                    .unlockPost(body.paymentMethodId, post, creator, user)

                val postPaymentIntent = PaymentResponse(
                    PaymentResponseAttributes(
                        status = paymentIntent.status,
                        subscriptionStatus = null,
                        createdAt = paymentIntent.created,
                        paymentIntentClientSecret = paymentIntent.intent?.clientSecret,
                        couponCode = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship(user.id),
                        post = PostDtoRelationship(post.id!!),
                        creator = post.relationships.user!!,
                    ),
                )

                Response(paymentIntent.httpStatus)
                    .body(postPaymentIntent)
            },
        )

    data class PostUnlockRequest(
        val postId: String,
        val paymentMethodId: String,
    )

    @Suppress("unused")
    val routeDeleteStripeAccount: ContractRoute =
        ("/v1/users" / Path.userId().of("creatorId") / "stripe-account").delete(
            summary = "Deletes given user's stripe account. Only accessible for moderators.",
            tag = "Stripe",
            parameters = object {},
            responses = listOf(
                Status.NO_CONTENT to Unit,
                Status.NOT_FOUND to Unit,
            ),
            handler = { request, _, creatorId, _ ->
                val user = userRepository.get(request)
                if (user.role != Role.MODERATOR) {
                    throw ForbiddenException(
                        "User ${user.id} is not a moderator to delete creator's stripe account.",
                        mapOf("userId" to user.id),
                    )
                }
                val creator = userRepository.get(creatorId)
                val stripeAccountId = creator.creator.stripeAccountId
                    ?: throw BadRequestException(
                        "User ${creator.id} does not have a stripe account assigned.",
                        mapOf("userId" to creator.id),
                    )

                log.info(
                    "Deleting stripe account ${creator.creator.stripeAccountId} of $creatorId.",
                    mapOf("userId" to creator.id),
                )
                val tier = Tier.ofId(creator.creator.tierId)
                try {
                    stripe.deleteAccount(
                        stripeAccountId,
                        tier.currency,
                        "Manual delete of connected account",
                        user.id,
                    )
                } catch (e: Exception) {
                    throw ServerException(
                        "Failed to delete $stripeAccountId of ${creator.id}: ${e.message}",
                        mapOf("userId" to creator.id),
                        e,
                    )
                } finally {
                    userRepository.deleteStripeAccountFromUser(creatorId, stripeAccountId)
                }

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    @Deprecated("Remove when HH-2305 is done.")
    val routeUsStripeAccountsLinks: ContractRoute =
        "/v1/us-accounts".get(
            summary = "List US stripe accounts to verify.",
            tag = "Stripe",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT example Unit),
            handler = { request, _ ->
                val user = userRepository.get(request)
                if (user.role != Role.MODERATOR) {
                    throw ForbiddenException(
                        "User ${user.id} is not a moderator to delete creator's stripe account.",
                        mapOf("userId" to user.id),
                    )
                }
                val (_, _, _, _, clients) = initializeStripeScript(SystemEnv.isProduction)
                val body = clients[Currency.PLN].accounts()
                    .list()
                    .autoPagingIterable()
                    .toList()
                    .parallelStream()
                    .map { account ->
                        val link = stripeAccountService.createAccountLink(user.id, account.id, "", Currency.PLN)
                        "https://dashboard.stripe.com/connect/accounts/${account.id}\n" +
                            "\t${account.email} ${account.individual?.lastName ?: account.company?.name}" +
                            " ${account.businessProfile?.name}\n" +
                            "\tcharges:${account.chargesEnabled} ${account.requirements.currentlyDue}\n" +
                            "\t${link.url}"
                    }
                    .toList()
                    .joinToString("\n\n")

                Response(Status.OK)
                    .contentType(ContentType.TEXT_PLAIN)
                    .body(body)
            },
        )
}

private val subscriberSorts = SubscribersSort.entries.map { it.name.lowercase() }
private val newest = SubscribersSort.NEWEST.name.lowercase()
private val refundMethods = RefundMethod.entries.map { "`$it`" }.joinToString(", ")
private val refundMethodDescription = """
    Values differ only for moderators – possible values are [$refundMethods}].
    When cancelling by Creator or User, the value must be `null`.
    Cancels by Creators are always refunded, cancels by users are never refunded.
""".trimIndent().replace("\\s+".toRegex(), " ")

private const val CHARGES_PAGE_SIZE = 10

data class CreateSubscriptionPostBody(
    val paymentMethodId: String? = null,
    val couponId: String? = null,
    val cardCreateType: CardCreateType? = null,
)

data class PaymentResponse(
    val attributes: PaymentResponseAttributes,
    val relationships: PaymentResponseRelationships,
)

data class PaymentResponseAttributes(
    val status: PaymentIntentStatus,
    val stripeId: String? = null,
    val subscriptionStatus: SubscriberStatus?,
    val createdAt: Instant?,
    val paymentIntentClientSecret: String?,
    val couponCode: String?,
)

data class PaymentResponseRelationships(
    val user: UserDtoRelationship,
    val post: PostDtoRelationship?,
    val creator: UserDtoRelationship?,
)
