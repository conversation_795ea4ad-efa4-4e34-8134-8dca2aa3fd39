package hero.api.user.service

import hero.baseutils.log
import hero.baseutils.minusMinutes
import hero.core.logging.Logger
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.model.CancelledByRole
import hero.model.DeletedReason
import hero.model.ModeratorPermission
import hero.model.Role
import hero.model.SignInProvider
import hero.model.User
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.model.UserStatus
import hero.model.topics.EmailPublished
import hero.model.topics.UserDeleted
import hero.repository.session.SessionRepository
import java.time.Clock
import java.time.Instant
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.UUID

class DeleteUserCommandService(
    private val usersCollection: TypedCollectionReference<User>,
    private val sessionRepository: SessionRepository,
    private val pubSub: PubSub,
    private val logger: Logger = log,
    private val clock: Clock = Clock.systemUTC(),
) {
    fun execute(command: DeleteUser) {
        val user = usersCollection[command.userId].get()
        if (user.status == UserStatus.DELETED) {
            logger.info("User ${user.id} is already deleted")
            return
        }

        val requester = usersCollection[command.requesterId].get()
        validateCommand(command, user, requester)

        if (!user.email.isNullOrBlank()) {
            pubSub.publish(
                EmailPublished(
                    to = user.email!!,
                    // https://app.eu.mailgun.com/app/sending/domains/mg.herohero.co/templates/details/sign-off-finished
                    template = "sign-off-finished",
                    variables = listOf(
                        "deleted-by-moderator" to requester.isModerator,
                        "deleted-reason" to command.deletedReason,
                    ),
                    language = user.language,
                ),
            )
        }

        val now = Instant.now(clock)
        user.copy(
            status = UserStatus.DELETED,
            deletedAt = Instant.now(clock),
            deletedReason = command.deletedReason,
            deletedNote = command.deletedNote,
            deletedBy = command.requesterId,
        ).let {
            if (it.email != null) {
                val todayDate = now.truncatedTo(ChronoUnit.SECONDS).atZone(ZoneOffset.UTC).toLocalDate()
                val suffix = "$todayDate-${now.epochSecond}"
                it.copy(email = "${user.email}-$suffix")
            } else {
                it
            }
        }.run {
            usersCollection[id].set(this)
        }

        pubSub.publish(
            UserStateChanged(
                stateChange = UserStateChange.DELETED,
                user = user,
            ),
        )
        pubSub.publish(
            UserDeleted(
                userId = user.id,
                cancelledBy = command.requesterId,
                cancelledByRole = if (requester.isModerator) CancelledByRole.MODERATOR else CancelledByRole.USER,
                cancelSubscriptions = command.cancelSubscriptions,
                refundSubscriptions = command.refundSubscriptions,
                deletedReason = command.deletedReason,
                deletedNote = command.deletedNote,
            ),
        )
        logger.info("Deleted ${user.id} with $command.", mapOf("userId" to user.id))
    }

    private fun validateCommand(
        command: DeleteUser,
        user: User,
        requester: User,
    ) {
        val supporters = user.counts.supporters
        if (requester.isModerator) {
            // moderator validations
            if (command.requesterId == command.userId) {
                val message = "Moderator ${command.requesterId} cannot delete themselves."
                logger.error(message, mapOf("userId" to command.requesterId))
                throw ForbiddenException(message)
            }

            if (!ModeratorPermission.canDeleteUsers(requester.moderatorPermissions)) {
                val message = "Moderator ${command.requesterId} is not empowered to delete users."
                logger.error(message, mapOf("userId" to command.requesterId))
                throw ForbiddenException(message)
            }
            if (supporters > 100) {
                throw ForbiddenException("Not even MODERATOR can delete an account with $supporters supporters")
            }

            val session = sessionRepository.getById(UUID.fromString(command.sessionId))
            if (session.userId != command.requesterId) {
                error("Got session ${session.id} for user ${command.requesterId}")
            }

            val passwordLogin = session.signInProvider == SignInProvider.PASSWORD
            val newSession = session.createdAt.isAfter(Instant.now(clock).minusMinutes(10))
            if (session.revoked || !passwordLogin || !newSession) {
                throw ForbiddenException()
            }
        } else {
            // ordinary user validations
            if (command.requesterId != command.userId) {
                val message = "User ${command.requesterId} is not allowed to delete a different user ${command.userId}."
                logger.error(message, mapOf("userId" to command.requesterId))
                throw ForbiddenException(message)
            }

            if (command.refundSubscriptions) {
                throw ForbiddenException(
                    "When user ${command.requesterId} deletes themselves, refunds are not allowed.",
                )
            }
        }

        if (!command.cancelSubscriptions && supporters > 0) {
            error(
                "Cannot delete account ${user.id} with active supporters ($supporters)" +
                    " and cancelSubscriptions set to false.",
            )
        }
    }
}

data class DeleteUser(
    val userId: String,
    val requesterId: String,
    val sessionId: String,
    val cancelSubscriptions: Boolean,
    val refundSubscriptions: Boolean,
    val deletedReason: DeletedReason,
    val deletedNote: String?,
)

private val User.isModerator
    get() = role == Role.MODERATOR
