.Kotlin/Run/Api/variables:
  variables:
    SERVICE_NAME: api
    SENTRY_DSN: https://<EMAIL>/4506044733325312
    SENTRY_ORG: herohero
    SENTRY_PROJECT: api
    ENV_VARS: "SQL_MAX_POOL_SIZE=2,FF_PRIVATE_PROFILES_ENABLED=true"
    ADDITIONAL_PARAMETERS: "--liveness-probe=httpGet.port=8080,httpGet.path=/liveness"

Kotlin/Run/Api/build:
  stage: build-services
  needs:
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/Http4k/build
    - Kotlin/Modules/Gjirafa/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Contract/build
    - Kotlin/Modules/Exceptions/build
    - Kotlin/Modules/IntegrationTesting/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Repository/build
  extends:
    - .Kotlin/Run/Api/variables
    - .Kotlin/job-build-gradle-service

Kotlin/Run/Api/deploy-devel:
  stage: deploy-devel
  extends:
    - .Kotlin/Run/deploy-devel
    - .Kotlin/Run/Api/variables
  needs:
    - Kotlin/Run/Api/build
  variables:
    MEMORY: 1024Mi

Kotlin/Run/Api/deploy-staging:
  stage: deploy-staging
  extends:
    - .Kotlin/Run/deploy-staging
    - .Kotlin/Run/Api/variables
  needs:
    - Kotlin/Run/Api/build
  variables:
    SQL_DATABASE_NAME: prod-herohero-db
    MEMORY: 1024Mi

Kotlin/Run/Api/deploy-prod:
  stage: deploy-prod
  extends:
    - .Kotlin/Run/deploy-prod
    - .Kotlin/Run/Api/variables
  needs:
    - Kotlin/Run/Api/build
  variables:
    CPU: 2
    MEMORY: 4096Mi
    MAX_CONCURRENT_REQUESTS: 160
    THROTTLING: "no"
    MIN_INSTANCES: 2
    MAX_INSTANCES: 16
    ENV_VARS: "SQL_MAX_POOL_SIZE=15"
