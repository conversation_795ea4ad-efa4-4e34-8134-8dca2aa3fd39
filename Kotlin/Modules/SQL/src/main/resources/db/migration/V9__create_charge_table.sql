CREATE TABLE charge
(
    stripe_id           TEXT PRIMARY KEY,
    customer_id         TEXT      NOT NULL,
    amount              BIGINT    NOT NULL,
    amount_captured     BIGINT    NOT NULL,
    amount_refunded     BIGINT    NOT NULL,
    payment_intent_id   TEXT      NOT NULL,
    status              TEXT      NOT NULL,
    stripe_created_at   TIMESTAMP NOT NULL,
    payment_method_id   TEXT      NOT NULL,
    succeeded_charges   INT       NOT NULL,
    currency            TEXT      NOT NULL,
    refunded            BOOL      NOT NULL,
    disputed            BOOL      NOT NULL,
    description         TEXT      NULL,
    payment_method_type TEXT      NULL,
    card_brand          TEXT      NULL,
    card_country        TEXT      NULL,
    card3ds             TEXT      NULL,
    card_cvc_check      TEXT      NULL,
    failure_code        TEXT      NULL,
    failure_message     TEXT      NULL
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE charge TO "<EMAIL>";
        END IF;
    END
$$;
