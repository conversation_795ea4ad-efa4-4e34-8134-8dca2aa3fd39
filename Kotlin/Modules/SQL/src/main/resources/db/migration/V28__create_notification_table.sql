CREATE TABLE notification
(
    id             TEXT PRIMARY KEY,
    user_id        TEXT        NOT NULL,
    type           TEXT        NOT NULL,
    actor_ids      TEXT[]      NOT NULL,
    object_post_id TEXT        NULL,
    object_user_id TEXT        NULL,
    created_at     TIMESTAMPTZ NOT NULL,
    seen_at        TIMESTAMPTZ NULL,
    checked_at     TIMESTAMPTZ NULL,
    deleted_at     TIMESTAMPTZ NULL,

    CONSTRAINT "e7c79729d8304b89926256c077307688_fk" FOREIGN KEY (user_id) REFERENCES "user" (id) ON DELETE CASCADE,
    CONSTRAINT "1cae51c7c0b4434abc5821540316ab98_fk" FOREIGN KEY (object_post_id) REFERENCES post (id) ON DELETE CASCADE,
    CONSTRAINT "43432dd71246435aa6982eace8ec7832_fk" FOREIGN KEY (object_user_id) REFERENCES "user" (id) ON DELETE CASCADE
);

CREATE INDEX "76c035eccc8a4647b5714cb82bb65d24_ix" ON notification (user_id);
CREATE INDEX "b0f9ce8f67884022b388a4eaad00db79_ix" ON notification (created_at);
CREATE INDEX "f82f4e09536a4b51b98455c0e3b5edc5_ix" ON notification (deleted_at);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE notification TO "<EMAIL>";
        END IF;
    END
$$;
