CREATE TABLE oauth_authorization_code
(
    id             TEXT PRIMARY KEY,
    client_id      UUID        NOT NULL,
    user_id        TEXT        NOT NULL,
    redirect_uri   TEXT        NOT NULL,
    state          TEXT        NULL,
    scopes         TEXT[]      NOT NULL,
    response_type  TEXT        NOT NULL,
    response_mode  TEXT        NULL,
    code_challenge TEXT        NULL,
    created_at     TIMESTAMPTZ NOT NULL,
    updated_at     TIMESTAMPTZ NOT NULL,
    used_at        TIMESTAMPTZ NULL,

    CONSTRAINT "a51d0f413cf64f2f9f900fa75730d332_fk" FOREIGN KEY (client_id) REFERENCES oauth_client (id),
    CONSTRAINT "127071c9932e43d7890b538bec01d215_fk" FOREIGN KEY (user_id) REFERENCES "user" (id)
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE oauth_authorization_code TO "<EMAIL>";
        END IF;
    END
$$;
