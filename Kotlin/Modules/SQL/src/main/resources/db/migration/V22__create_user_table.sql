CREATE TABLE "user"
(
    id                          TEXT        NOT NULL PRIMARY KEY,
    name                        TEXT        NOT NULL,
    -- this should be unique once we have all users in the database
    email                       TEXT        NULL,

    -- we should create a map instead, for all the languages instead of columns
    bio                         TEXT        NOT NULL,
    bio_html                    TEXT        NOT NULL,
    bio_en                      TEXT        NOT NULL,
    bio_html_en                 TEXT        NOT NULL,

    -- this should be unique once we have all users in the database
    path                        TEXT        NOT NULL,
    role                        TEXT        NOT NULL,

    language                    TEXT        NOT NULL,
    status                      TEXT        NOT NULL,

    firebase_id                 TEXT        NULL,
    facebook_id                 TEXT        NULL,
    google_id                   TEXT        NULL,
    airtable_id                 TEXT        NULL,


    -- flags
    is_explicit                 BOOLEAN     NOT NULL,
    is_featured                 BOOLEAN     NOT NULL,
    featured_in_languages       TEXT[]      NOT NULL,
    has_livestreams             BOOLEAN     NOT NULL,
    has_spotify_export          BOOLEAN     NOT NULL,
    has_drm                     BOOLEAN     NOT NULL,
    has_rss_feed                BOOLEAN     NOT NULL,

    -- image
    profile_image_url           TEXT        NULL,
    profile_image_width         INT         NULL,
    profile_image_height        INT         NULL,

    -- meta ignored

    -- counts
    subscribers_count           INT         NOT NULL,
    subscriptions_count         INT         NOT NULL,
    income_raw                  BIGINT      NOT NULL,
    income_clean                BIGINT      NOT NULL,
    invoices_count              INT         NOT NULL,
    posts_count                 INT         NOT NULL,
    payments                    INT         NOT NULL,

    customer_ids                JSONB       NOT NULL,

    -- 3rd party metas
    discord                     JSONB       NULL,
    spotify                     JSONB       NULL,
    spotify_uri                 TEXT        NULL,
    spotify_feed_ready          BOOLEAN     NULL,
    gjirafa_livestream          JSONB       NULL,
    analytics                   JSONB       NULL,

    creator                     JSONB       NOT NULL,
    company                     JSONB       NULL,

    -- timestamps
    created_at                  TIMESTAMPTZ NOT NULL,
    updated_at                  TIMESTAMPTZ NOT NULL,
    deleted_at                  TIMESTAMPTZ NULL,
    path_changed_at             TIMESTAMPTZ NOT NULL,
    one_stop_shop_at            TIMESTAMPTZ NULL,
    privacy_policy_effective_at TIMESTAMPTZ NULL,
    last_post_at                TIMESTAMPTZ NULL,
    last_charge_failed_at       TIMESTAMPTZ NULL,

    is_of_age                   BOOLEAN     NOT NULL,
    moderator_permissions       INT         NULL
);

CREATE TABLE notification_settings
(
    user_id        TEXT    NOT NULL PRIMARY KEY,
    email_new_post BOOLEAN NOT NULL,
    email_new_dm   BOOLEAN NOT NULL,
    newsletter     BOOLEAN NOT NULL,
    terms_changed  BOOLEAN NOT NULL
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE "user" TO "<EMAIL>";
            GRANT ALL ON TABLE notification_settings TO "<EMAIL>";
        END IF;
    END
$$;
