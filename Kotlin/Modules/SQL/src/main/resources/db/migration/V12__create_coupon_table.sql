CREATE TABLE coupon
(
    stripe_id          TEXT PRIMARY KEY,
    creator_id         TEXT        NOT NULL,
    created_at         TIMESTAMPTZ NOT NULL,
    target             TEXT        NOT NULL,
    method             TEXT        NOT NULL,
    purchased_by       TEXT        NOT NULL,
    amount_off         INT         NULL,
    currency           TEXT        NULL,
    duration           TEXT        NOT NULL,
    duration_in_months INT         NULL,
    name               TEXT        NULL,
    percent_off        FLOAT       NULL,
    max_redemptions    INT         NULL,
    times_redeemed     INT         NOT NULL,
    redeem_by          TIMESTAMPTZ NULL,
    valid              BOOLEAN     NOT NULL,
    campaign           TEXT        NULL,
    tier_id            TEXT        NULL,
    deleted_at         TIMESTAMPTZ NULL
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE coupon TO "<EMAIL>";
        END IF;
    END
$$;
