CREATE MATERIALIZED VIEW IF NOT EXISTS name_histogram AS
SELECT name, COUNT(*) AS count
FROM (SELECT unnest(regexp_split_to_array(lower(unaccent(trim(name))), '\s+')) AS name FROM "user") AS name_parts
WHERE name <> ''
GROUP BY name
ORDER BY count DESC;

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE name_histogram TO "<EMAIL>";
        END IF;
    END
$$;

CREATE INDEX IF NOT EXISTS "dd9bd6e9a6fd4170b5f8bb388aab69d4_ix"
    ON name_histogram
        USING gin (immutable_unaccent(name) gin_trgm_ops);

CREATE INDEX IF NOT EXISTS "3c1bc1b4724c4f03b21514871b2f6c69_ix"
    ON "user"
        USING gist (immutable_unaccent(name) gist_trgm_ops(siglen=128)) WHERE status = 'ACTIVE';
