CREATE TABLE api_key
(
    id           UUID PRIMARY KEY,
    key          TEXT        NOT NULL,
    user_id      TEXT        NOT NULL,
    created_at   TIMESTAMPTZ NOT NULL,
    updated_at   TIMESTAMPTZ NOT NULL,
    expires_at   TIMESTAMPTZ NULL,
    revoked_at   TIMESTAMPTZ NULL,
    last_used_at TIMESTAMPTZ NULL,

    CONSTRAINT "680b3772aad94ddf801aaba25e656671_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "145bcc3314ce432b896bf0b0548146ba_ux" UNIQUE (key)
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE api_key TO "<EMAIL>";
        END IF;
    END
$$;
