package hero.stripe.service.scripts

import com.stripe.StripeClient
import com.stripe.model.Account
import com.stripe.param.AccountUpdateParams
import com.stripe.param.TokenCreateParams
import hero.baseutils.SystemEnv
import hero.model.Creator
import hero.model.Currency
import hero.model.User
import hero.model.UserCompany
import hero.stripe.service.StripeAccountService
import hero.stripe.service.StripeClients
import java.time.Instant

fun deBankAccountToken(): TokenCreateParams.BankAccount =
    TokenCreateParams.BankAccount.builder()
        // https://stripe.com/docs/connect/testing#account-numbers
        .setAccountNumber("**********************")
        .setCurrency("EUR")
        .setCountry("DE")
        .build()

fun usBankAccountToken(): TokenCreateParams.BankAccount =
    TokenCreateParams.BankAccount.builder()
        // https://stripe.com/docs/connect/testing#account-numbers
        .setRoutingNumber("*********")
        .setAccountNumber("************")
        .setCurrency("USD")
        .setCountry("US")
        .build()

/**
 * Method used to setup testing verified account. Note that it might take
 * a few minutes for Stripe to make the account verified (which is in contradiction
 * with docs in https://stripe.com/docs/connect/testing).
 */
fun Account.verifyForTesting(client: StripeClient): Account =
    update(
        AccountUpdateParams.builder()
            .setBusinessType(AccountUpdateParams.BusinessType.INDIVIDUAL)
            .setExternalAccount(
                client.tokens().create(
                    TokenCreateParams.builder()
                        .setBankAccount(
                            when (country) {
                                // see: https://stripe.com/docs/connect/testing
                                "US" -> usBankAccountToken()
                                "DE" -> deBankAccountToken()
                                else -> error("Unknown testing country: $country")
                            },
                        )
                        .build(),
                ).id,
            )
            .setTosAcceptance(
                AccountUpdateParams.TosAcceptance.builder()
                    .setDate(Instant.now().epochSecond)
                    .setIp("***********")
                    .setServiceAgreement("full")
                    .build(),
            )
            .setIndividual(
                AccountUpdateParams.Individual.builder()
                    .setFirstName("Jane")
                    .setLastName("Doe $country")
                    .setPhone("+****************")
                    // https://stripe.com/docs/connect/testing#identity-and-address-verification
                    .setDob(AccountUpdateParams.Individual.Dob.builder().setDay(1).setMonth(1).setYear(1902).build())
                    .setIdNumber("*********")
                    .setAddress(
                        AccountUpdateParams.Individual.Address.builder()
                            .setState(
                                when (country) {
                                    "DE" -> "DE"
                                    "US" -> "NY"
                                    else -> error("Unknown testing country: $country")
                                },
                            )
                            .build(),
                    )
                    .setVerification(
                        AccountUpdateParams.Individual.Verification.builder()
                            .setDocument(
                                AccountUpdateParams.Individual.Verification.Document.builder()
                                    // https://stripe.com/docs/connect/testing#test-file-tokens
                                    .setFront("file_identity_document_success")
                                    .build(),
                            )
                            .build(),
                    )
                    .build(),
            )
            .build(),
    )

// As Stripe is not able to manually create account which would be instantly verified (and ready to use),
// (see: https://dashboard.stripe.com/support/sco_O5oQc7RSJsDIDj) we need to pre-generate these and store
// their IDs. Run this method to generate a single testing account.
fun main() {
    val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    val stripe = StripeAccountService(
        clients = stripeClients,
        hostname = "https://devel.herohero.co",
        hostnameServices = "https://svc-devel.herohero.co",
    )

    val country = "DE"
    val currency = Currency.EUR

    val user = User(
        id = "janedoeid",
        email = "permanent-account-${country.lowercase()}@herohero.co",
        name = "Testing Connected Account [$country] (do not delete)",
        path = "janedoeid",
        creator = Creator(tierId = "${currency}05"),
        company = UserCompany(
            name = "Testing Connected Account [$country]",
            city = when (country) {
                "DE" -> "Berlin"
                "US" -> "Schenectady"
                else -> error("Unknown testing country: $country")
            },
            postalCode = when (country) {
                "DE" -> "10115"
                "US" -> "12345"
                else -> error("Unknown testing country: $country")
            },
            // https://stripe.com/docs/connect/testing#test-verification-addresses
            address = "address_full_match",
            country = country,
        ),
    )

    val account = stripe
        .createAccount(user)
        .verifyForTesting(stripeClients[currency])

    println("https://dashboard.stripe.com/test/connect/accounts/${account.id}/activity")
}
