package hero.stripe.service

import com.apple.itunes.storekit.advancedcommerce.AdvancedCommerceInAppSignatureCreator
import com.apple.itunes.storekit.model.AdvancedCommerceInAppRequest
import com.apple.itunes.storekit.model.Environment
import com.apple.itunes.storekit.model.ResponseBodyV2DecodedPayload
import com.apple.itunes.storekit.verification.SignedDataVerifier
import hero.jwt.classLoader
import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jws
import io.jsonwebtoken.JwtParser
import io.jsonwebtoken.Jwts
import java.security.PublicKey
import java.security.cert.CertificateFactory
import java.util.Base64

/**
 * Generate Apple P8 key here:
 *   https://appstoreconnect.apple.com/access/integrations/api/subs
 *   https://appstoreconnect.apple.com/access/integrations/api
 *
 * Signature generated with:
 *   https://github.com/apple/app-store-server-library-java
 */
class AppleSigningService(
    private val production: Boolean,
    // TODO if (production) 6477841374 else 6743796440,
    appId: Long = 6477841374,
    bundleId: String = "co.herohero.Herohero",
    keyId: String = "M6N8N6JYFA",
    issuerId: String = "406dba29-8f22-47d5-ab52-dcc6549915cc",
    environment: Environment = if (production) Environment.PRODUCTION else Environment.SANDBOX,
) {
    private val rootCAs = setOf(
        classLoader.getResourceAsStream("AppleIncRootCertificate.cer"),
        classLoader.getResourceAsStream("AppleRootCA-G2.cer"),
        classLoader.getResourceAsStream("AppleRootCA-G3.cer"),
    )

    private val signedPayloadVerifier = SignedDataVerifier(rootCAs, bundleId, appId, environment, true)

    private val appleInAppKeyBytes = classLoader.getResourceAsStream("apple_in_app_key.p8")?.readAllBytes()
        ?: error("Key file not found in resources")

    private val signatureCreator =
        AdvancedCommerceInAppSignatureCreator(String(appleInAppKeyBytes), keyId, issuerId, bundleId)

    fun sign(request: AdvancedCommerceInAppRequest): String = signatureCreator.createSignature(request)

    fun decodeNotification(signedPayload: String): ResponseBodyV2DecodedPayload =
        signedPayloadVerifier.verifyAndDecodeNotification(signedPayload)

    /** https://developer.apple.com/documentation/appstoreserverapi/jwstransactiondecodedpayload */
    fun parseX5cPayload(jwt: String): Jws<Claims> {
        // Step 1: Parse JWT header to extract x5c
        val parts = jwt.split(".")
        if (parts.size != 3) error("Invalid JWT format")

        val headerJson = String(Base64.getUrlDecoder().decode(parts[0]))
        val x5cRegex = Regex(""""x5c"\s*:\s*\[\s*"([^"]+)"""")
        val match = x5cRegex.find(headerJson)
            ?: error("x5c certificate not found in JWT header")

        val certBase64 = match.groupValues[1]

        // Step 2: Convert x5c to PublicKey
        val certBytes = Base64.getDecoder().decode(certBase64)
        val certFactory = CertificateFactory.getInstance("X.509")
        val cert = certFactory.generateCertificate(certBytes.inputStream())
        val publicKey: PublicKey = cert.publicKey

        // Step 3: Use the public key to validate the JWT
        val jwtParser: JwtParser = Jwts.parser()
            .verifyWith(publicKey)
            .build()

        return jwtParser.parseSignedClaims(jwt)
    }
}
