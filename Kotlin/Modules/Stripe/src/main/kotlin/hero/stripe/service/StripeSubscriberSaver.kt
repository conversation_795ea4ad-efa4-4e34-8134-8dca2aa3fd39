package hero.stripe.service

import com.stripe.model.Subscription
import hero.baseutils.instantOf
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.isTrue
import hero.gcloud.where
import hero.model.CancelledByRole
import hero.model.CouponMethod
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriberType
import hero.model.Tier
import hero.model.topics.SubscriberChanged
import java.time.Instant

class StripeSubscriberSaver(
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
    private val tiersCollection: TypedCollectionReference<Tier>,
    private val appleSubscriptionService: AppleSubscriptionService,
    private val pubSub: PubSub,
) {
    fun save(
        userId: String,
        creatorId: String,
        stripeSubscription: Subscription,
        isNewSubscription: Boolean,
    ): Subscriber {
        val status = enumValueOf<SubscriberStatus>(
            stripeSubscription.status.replace("canceled", "cancelled").uppercase(),
        )
        val metadata = stripeSubscription.metadata
        val subscribed = metadata[Subscriber::subscribed.name]?.let { instantOf(it) }
            ?: Instant.ofEpochSecond(stripeSubscription.startDate)
        val couponMethod = metadata[Subscriber::couponMethod.name]?.let { CouponMethod.valueOf(it) }

        val subscriber = getSubscriberOrDefault(userId, creatorId, status, subscribed, stripeSubscription)
            .let { subscriber ->
                subscriber.copy(
                    tierId = metadata["tierId"] ?: getDefaultTier().id,
                    status = status,
                    cancelAtPeriodEnd = stripeSubscription.cancelAtPeriodEnd,
                    // for re-subscriptions we want to keep original subscribe date
                    subscribed = subscribed,
                    expires = Instant.ofEpochSecond(stripeSubscription.currentPeriodEnd),
                    refunded = metadata[Subscriber::refunded.name]?.toBoolean() ?: false,
                    refused = metadata[Subscriber::refused.name]?.toBoolean() ?: false,
                    cancelledReason = metadata[Subscriber::refusedReason.name]
                        ?: metadata[Subscriber::cancelledReason.name],
                    couponAppliedForMonths = metadata[Subscriber::couponAppliedForMonths.name]?.toLong(),
                    couponAppliedForDays = metadata[Subscriber::couponAppliedForDays.name]?.toLong(),
                    couponExpiresAt = metadata[Subscriber::couponExpiresAt.name]?.let { instantOf(it) },
                    // The user receives a coupon, triggering the creation of a Subscriber entity.
                    // Subsequently, a notification will be sent to them, and a flag will be set for a specific date.
                    // If the user subscribes again using the coupon,
                    // and the flag is not reset here, no notification is sent afterward.
                    couponId = stripeSubscription.discount?.coupon?.id,
                    couponExpiresNotifiedAt = if (isNewSubscription) null else subscriber.couponExpiresNotifiedAt,
                    cancelAt = stripeSubscription.cancelAt?.let { Instant.ofEpochSecond(it) },
                    cancelledAt = stripeSubscription.canceledAt?.let { Instant.ofEpochSecond(it) },
                    // Unfortunately, when cancelling via Stripe UI, the admin is not stored anywhere.
                    cancelledBy = metadata[Subscriber::cancelledBy.name],
                    cancelledByRole = metadata[Subscriber::cancelledByRole.name]?.let {
                        enumValueOf<CancelledByRole>(it)
                    },
                    resubscribedAt = metadata[Subscriber::resubscribedAt.name]?.let { instantOf(it) },
                    couponMethod = couponMethod,
                    couponPercentOff = metadata[Subscriber::couponPercentOff.name]?.toLongOrNull(),
                    couponCampaign = metadata[Subscriber::couponCampaign.name],
                    appleReferenceId = metadata[Subscriber::appleReferenceId.name],
                    appleTransactionId = metadata[Subscriber::appleTransactionId.name],
                )
            }

        if (subscriber.status == SubscriberStatus.CANCELLED && subscriber.appleReferenceId != null) {
            if (subscriber.appleTransactionId == null) {
                error("Field `appleTransactionId` was nul for ${subscriber.id}/${subscriber.appleReferenceId}.")
            }
            appleSubscriptionService.cancelAtPeriodEnd(subscriber.appleTransactionId!!, subscriber.appleReferenceId!!)
        }

        subscribersCollection[subscriber.id].set(subscriber)
        pubSub.publish(
            SubscriberChanged(
                userId = subscriber.userId,
                creatorId = subscriber.creatorId,
            ),
        )
        return subscriber
    }

    private fun getSubscriberOrDefault(
        userId: String,
        creatorId: String,
        status: SubscriberStatus,
        subscribedAt: Instant,
        stripeSubscription: Subscription,
    ): Subscriber =
        getSubscriber(userId, creatorId) ?: Subscriber(
            userId = userId,
            creatorId = creatorId,
            tierId = stripeSubscription.metadata["tierId"] ?: getDefaultTier().id,
            status = status,
            subscribed = subscribedAt,
            subscriberType = SubscriberType.STRIPE,
            couponId = stripeSubscription.discount?.coupon?.id,
            couponMethod = stripeSubscription.metadata[Subscriber::couponMethod.name]?.let { CouponMethod.valueOf(it) },
            couponPercentOff = stripeSubscription.metadata[Subscriber::couponPercentOff.name]?.toLongOrNull(),
        )

    private fun getSubscriber(
        userId: String,
        creatorId: String,
    ): Subscriber? =
        subscribersCollection
            .where(Subscriber::userId).isEqualTo(userId)
            .and(Subscriber::creatorId).isEqualTo(creatorId)
            .and(Subscriber::subscriberType).isEqualTo(SubscriberType.STRIPE)
            .fetchSingle()

    private fun getDefaultTier(): Tier =
        tiersCollection.where(Tier::default).isTrue().fetchSingle() ?: error("Missing default tier")
}
