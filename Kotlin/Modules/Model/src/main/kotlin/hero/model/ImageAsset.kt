package hero.model

import hero.core.annotation.NoArg
import java.net.URL

// TODO make private constructor when the issue bellow is ready
// see: https://youtrack.jetbrains.com/issue/KT-11914/Confusing-data-class-copy-with-private-constructor
@NoArg
data class ImageAsset(
    val id: String,
    val width: Int,
    val height: Int,
    val hidden: Boolean = false,
) {
    /**
     *  Validation of id URL must be in this method since QueryDocumentSnapshot.to initializes the object
     *  empty (due to NoArg). This behaviour is fixed when TypedCollectionReference is used.
     */
    companion object {
        fun of(
            id: String,
            width: Int,
            height: Int,
        ): ImageAsset {
            URL(id)
            return ImageAsset(id, width, height)
        }
    }
}
