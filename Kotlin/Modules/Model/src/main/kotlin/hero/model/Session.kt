package hero.model

import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant

@NoArg
data class Session(
    val id: String,
    val userId: String,
    val userAgent: String,
    val ipAddress: String?,
    val signInLocation: String?,
    val createdAt: Instant = Instant.now(),
    val refreshedAt: Instant = Instant.now(),
    val revoked: Boolean = false,
    val deviceId: String? = null,
    val signInProvider: SignInProvider? = null,
) {
    companion object : EntityCollection<Session> {
        override val collectionName: String = "sessions"
    }
}

enum class SignInProvider {
    PASSWORD,
    GOOGLE,
    APPLE,
    FACEBOOK,
}
