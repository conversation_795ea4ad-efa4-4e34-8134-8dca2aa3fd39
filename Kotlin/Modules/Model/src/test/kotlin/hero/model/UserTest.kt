package hero.model

import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class UserTest {
    @Test
    fun `restore intentionally broken emails for invoice delivery`() {
        assertEquals(
            "<EMAIL>",
            "<EMAIL>".restoreEmail(),
        )
        assertEquals(
            "<EMAIL>",
            "<EMAIL>-2023-12-29T19:06:18".restoreEmail(),
        )
        assertEquals(
            "<EMAIL>",
            "<EMAIL>-2023-12-29T19:06:18.749532233Z".restoreEmail(),
        )
    }
}
