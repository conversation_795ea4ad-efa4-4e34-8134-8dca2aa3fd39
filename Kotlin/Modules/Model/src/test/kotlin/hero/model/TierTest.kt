package hero.model

import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class TierTest {
    @Test
    fun `EUR tier correctly parsed and computed`() {
        val tierEur06 = Tier.ofId("EUR06")
        assertEquals(Currency.EUR, tierEur06.currency)
        assertEquals(6_00, tierEur06.priceCents)
        assertEquals(BigDecimal("0.60"), tierEur06.feeAbsolute)
        assertEquals(BigDecimal("10.00"), tierEur06.feePercents)
        assertEquals(BigDecimal("0.00"), tierEur06.stripeFeeDynamic)
        assertEquals(BigDecimal("0.00"), tierEur06.stripeFeeFixed)

        val tierEur13 = Tier.ofId("EUR13")
        assertEquals(13_00, tierEur13.priceCents)
        assertEquals(Currency.EUR, tierEur13.currency)
        assertEquals(BigDecimal("1.30"), tierEur13.feeAbsolute)
        assertEquals(BigDecimal("10.00"), tierEur13.feePercents)
        assertEquals(BigDecimal("0.00"), tierEur13.stripeFeeDynamic)
        assertEquals(BigDecimal("0.00"), tierEur13.stripeFeeFixed)

        val tierEur100 = Tier.ofId("EUR100")
        assertEquals(100_00, tierEur100.priceCents)
        assertEquals(Currency.EUR, tierEur100.currency)
        assertEquals(BigDecimal("10.00"), tierEur100.feeAbsolute)
        assertEquals(BigDecimal("10.00"), tierEur100.feePercents)
        assertEquals(BigDecimal("0.00"), tierEur100.stripeFeeDynamic)
        assertEquals(BigDecimal("0.00"), tierEur100.stripeFeeFixed)
    }

    @Test
    fun `USD tier correctly parsed and computed`() {
        val tierUsd06 = Tier.ofId("USD06")
        assertEquals(Currency.USD, tierUsd06.currency)
        assertEquals(6_00, tierUsd06.priceCents)
        assertEquals(BigDecimal("0.89"), tierUsd06.feeAbsolute)
        assertEquals(BigDecimal("14.83"), tierUsd06.feePercents)
        assertEquals(BigDecimal("2.90"), tierUsd06.stripeFeeDynamic)
        assertEquals(BigDecimal("0.30"), tierUsd06.stripeFeeFixed)

        val tierUsd13 = Tier.ofId("USD13")
        assertEquals(13_00, tierUsd13.priceCents)
        assertEquals(Currency.USD, tierUsd13.currency)
        assertEquals(BigDecimal("1.59"), tierUsd13.feeAbsolute)
        assertEquals(BigDecimal("12.23"), tierUsd13.feePercents)
        assertEquals(BigDecimal("2.90"), tierUsd13.stripeFeeDynamic)
        assertEquals(BigDecimal("0.30"), tierUsd13.stripeFeeFixed)

        val tierUsd100 = Tier.ofId("USD100")
        assertEquals(100_00, tierUsd100.priceCents)
        assertEquals(Currency.USD, tierUsd100.currency)
        assertEquals(BigDecimal("10.20"), tierUsd100.feeAbsolute)
        assertEquals(BigDecimal("10.20"), tierUsd100.feePercents)
        assertEquals(BigDecimal("2.90"), tierUsd100.stripeFeeDynamic)
        assertEquals(BigDecimal("0.30"), tierUsd100.stripeFeeFixed)
    }
}
