package hero.baseutils

import com.google.cloud.MetadataConfig
import com.google.cloud.MonitoredResource
import com.google.cloud.logging.LogEntry
import com.google.cloud.logging.LoggingOptions
import com.google.cloud.logging.Payload.StringPayload
import com.google.cloud.logging.Severity
import com.google.cloud.logging.SourceLocation
import com.google.cloud.logging.Synchronicity
import hero.core.logging.Logger
import io.sentry.Breadcrumb
import io.sentry.Sentry
import org.apache.logging.log4j.LogManager
import java.util.Collections
import org.apache.logging.log4j.Logger as ApacheLogger

val log4j: ApacheLogger = LogManager.getLogger()

val log = object : CloudLogger {
    // these must not fail when missing, otherwise we miss logs about erroring
    private val isLocal = systemEnvRelaxed("LOG_APPENDER") != "ConsoleFluentD"
    private val environment = systemEnvRelaxed("ENVIRONMENT") ?: "undefined"
    private val cloudProject = systemEnvRelaxed("CLOUD_PROJECT") ?: "undefined"
    private val serviceName = systemEnvRelaxed("SERVICE_NAME") ?: "undefined"
    private val cloudRegion = systemEnv("CLOUD_REGION")

    private val logName = "projects/$cloudProject/logs/run.googleapis.com/$serviceName".replace("/", "%2F")

    private val logging = LoggingOptions
        .getDefaultInstance()
        .getService()

    // this must be set to true for Cloud Functions
    override var synchronicity: Synchronicity
        get() = logging.writeSynchronicity
        set(value) {
            logging.writeSynchronicity = value
        }

    override fun log(
        severity: Severity,
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        // Log4J settings is not respected here, so we want to avoid
        // debugging messages here.
        if (environment == "prod" && severity <= Severity.DEBUG) {
            return
        }

        if (message != null) {
            Sentry.addBreadcrumb(severity.toBreadcrumb(message, properties, cause))
        }

        val sourceLocation = try {
            val caller = Thread.currentThread().stackTrace[STACK_TRACE_POSITION]
            // "${caller.className}.${caller.methodName}:${caller.lineNumber}"
            SourceLocation.newBuilder()
                .setFile(caller.className)
                .setFunction(caller.methodName)
                .setLine(caller.lineNumber.toLong())
                .build()
        } catch (
            @Suppress("SwallowedException") e: Throwable,
        ) {
            // this must not have any impact on logging
            null
        }

        try {
            val entry = LogEntry
                .newBuilder(
                    StringPayload.of(
                        (message ?: "Empty message.") +
                            (cause?.let { "\n" + it.stackTraceToString() } ?: ""),
                    ),
                )
                .setSeverity(severity)
                .setLogName(logName)
                .setSourceLocation(sourceLocation)
                .setResource(
                    MonitoredResource
                        .newBuilder("cloud_run_revision")
                        .setLabels(
                            // must respect following:
                            // https://cloud.google.com/monitoring/api/resources#tag_cloud_function
                            // https://cloud.google.com/monitoring/api/resources#tag_cloud_run_revision
                            mapOf(
                                "configuration_name" to environment,
                                "service_name" to "$environment-$serviceName",
                                // It seems this key used to be `region`, but they changed it to `location`,
                                // let's keep both for now to be sure.
                                "region" to cloudRegion,
                                "location" to cloudRegion,
                            ),
                        )
                        .build(),
                )
                .setLabels(
                    buildMap {
                        instanceId.value?.let {
                            put("instanceId", it)
                        }

                        properties
                            .entries
                            .take(MAX_LABELS_COUNT - 1)
                            .forEach { put(it.key, "${it.value}") }
                    },
                )
                .build()

            if (isLocal) {
                log4j.info(message, cause)
            } else {
                logging.write(Collections.singleton(entry))
            }
        } catch (e: Exception) {
            log4j.fatal(message, cause)
            @Suppress("ktlint:standard:max-line-length")
            log4j.fatal(
                "Couldn't cloud-log (if you are using cloud function, you might need to set synchronicity = SYNC): ${e.message}",
                e,
            )
        }
    }

    override fun warn(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log(Severity.WARNING, message, properties, cause)
    }

    override fun notice(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log(Severity.NOTICE, message, properties, cause)
    }

    override fun debug(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log(Severity.DEBUG, message, properties, cause)
    }

    override fun info(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log(Severity.INFO, message, properties, cause)
    }

    override fun error(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log(Severity.ERROR, message, properties, cause)
    }

    override fun fatal(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log(Severity.ALERT, message, properties, cause)
    }
}

interface CloudLogger : Logger {
    var synchronicity: Synchronicity

    fun log(
        severity: Severity,
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )
}

private const val MAX_LABELS_COUNT = 60
private const val STACK_TRACE_POSITION = 4

private fun Severity.toBreadcrumb(
    message: String,
    properties: Map<String, Any?>,
    cause: Throwable?,
) = when (this) {
    Severity.ERROR, Severity.ALERT -> Breadcrumb.error(message)
    Severity.INFO -> Breadcrumb.info(message)
    Severity.DEBUG -> Breadcrumb.debug(message)
    else -> Breadcrumb(message)
}.apply {
    category = "log"
    properties.forEach { (key, value) -> if (value != null) setData(key, value) }
    if (cause != null) {
        setData("cause", cause)
    }
}

private val instanceId = lazy {
    MetadataConfig.getInstanceId()
}
