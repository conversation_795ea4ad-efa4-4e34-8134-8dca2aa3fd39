package hero.baseutils

import com.github.kittinunf.fuel.core.FuelError
import com.github.kittinunf.fuel.core.Request
import com.github.kittinunf.fuel.core.extensions.cUrlString
import com.github.kittinunf.fuel.jackson.responseObject
import hero.jackson.jackson
import java.io.IOException
import java.net.HttpURLConnection.HTTP_BAD_GATEWAY
import java.net.HttpURLConnection.HTTP_CLIENT_TIMEOUT
import java.net.HttpURLConnection.HTTP_GATEWAY_TIMEOUT
import java.net.HttpURLConnection.HTTP_UNAVAILABLE

val defaultErrorHandler: (FuelError, Request) -> Unit = { error, request ->
    val responseBody = try {
        String(error.response.data)
    } catch (
        @Suppress("SwallowedException") e: Exception,
    ) {
        "{\"message\": \"Response payload is not available at this stage.\""
    }

    val messages = listOf(
        error.response.responseMessage,
        error.message,
        responseBody,
    ).filterNot { it.isNullOrEmpty() }

    val message = messages
        .joinToString(", ")
        .replace("\\s+".toRegex(), " ")

    throw when (error.response.statusCode) {
        // we retry by default on certain statuses and also dropped connections (-1)
        -1,
        HTTP_CLIENT_TIMEOUT, HTTP_TOO_MANY_REQUESTS, HTTP_BAD_GATEWAY, HTTP_UNAVAILABLE, HTTP_GATEWAY_TIMEOUT,
        -> IOException(
            message,
        )
        // fuel exceptions are not retried
        else -> FuelException(
            status = error.response.statusCode,
            message = message,
            responseBody = responseBody,
            cUrlString = try {
                request.cUrlString()
            } catch (e: Exception) {
                "[curl could not be generated: ${e.message}]"
            },
        )
    }
}

val fuelUserAgent = "Hero " + (systemEnvRelaxed("SERVICE_NAME") ?: "").capitalize() +
    " Service/${systemEnvRelaxed("MODULE_HASH") ?: ""} " +
    "(Java ${Runtime.version()}/Kotlin ${KotlinVersion.CURRENT}/Fuel)"

inline fun <reified T : Any> Request.fetch(noinline handler: (FuelError, Request) -> Unit = defaultErrorHandler) =
    retryOn(IOException::class) {
        val request = header("user-agent", fuelUserAgent)
        request
            .responseObject<T>(jackson)
            .third
            .run {
                component2()?.also { handler(it, request) }
                component1()!!
            }
    }

class FuelException(
    val status: Int,
    override val message: String,
    val responseBody: String?,
    val cUrlString: String?,
) : IllegalStateException() {
    override fun toString(): String = "$message\n$cUrlString"
}

private val googleEnvironment = SystemEnv.environment.replace("local", "devel")

fun serviceCall(
    service: String,
    path: String,
): String = "https://$googleEnvironment-$service-y7fnkx4lwq-ew.a.run.app$path"

fun publicFunctionCall(
    function: String,
    auth: Boolean = false,
): String = "https://svc-$googleEnvironment${if (auth) "" else "-na"}.herohero.co/$function/"

fun internalFunctionCall(function: String): String =
    "https://europe-west1-heroheroco.cloudfunctions.net/$googleEnvironment-$function"

private const val HTTP_TOO_MANY_REQUESTS = 429
