package hero.gjirafa.dto

import com.fasterxml.jackson.annotation.JsonProperty
import hero.model.Chapter
import java.time.Instant
import java.time.LocalDateTime
import hero.model.LiveVideoStatus as ModelLiveVideoStatus

data class GjirafaMultipartPart(
    val size: Long,
    val lastModified: Instant,
    val partNumber: Long,
    // do not remove this annotation as this is required because of Java Beans specification
    @get:JsonProperty("eTag")
    val eTag: String?,
)

// swagger (see V1 in upper left corner):
//   https://vp-bridge.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Analytics%20API%20V1
// spec: https://docs.google.com/document/d/1NrCnoPQ-qbkNkSvlaLe9clDFQnGSTEAuLse4F6raWZ8/edit
data class CreateVideoAnalyticsEventRequest(
    val uniqueViewId: String,
    val userId: String,
    val videoId: String,
    val adScheduleId: String?,
    val adId: String?,
    val playerId: String?,
    val playlistId: String?,
    val eventType: String = "videoevent",
    val countryCode: String?,
    val event: String?,
    val operationSystem: String?,
    val osVersion: String?,
    val deviceBrand: String?,
    val deviceModel: String?,
    val appIdentifier: String?,
    val appVersion: String?,
    val playerSdkVersion: String?,
    val extraData: String?,
    val duration: Double?,
    val subtitles: Boolean,
    val isLive: Boolean,
    val userAgent: String?,
    val domain: String?,
    val userIp: String?,
    val url: String?,
)

data class AddVideoWatchStateRequest(
    val uniqueViewId: String,
    val userId: String,
    val videoId: String,
    val playerId: String?,
    val playlistId: String?,
    val eventType: String = "watchState",
    val playerState: Boolean,
    val muted: Boolean,
    val currentTime: Double,
    val segmentWatchTime: Int?,
    val volume: Double,
    val fullScreen: Boolean,
    val insertTime: Instant?,
    val userAgent: String?,
    val duration: Double?,
    val isLive: Boolean,
)

// https://audio-analytics-dev.vpplayer.tech/swagger/index.html
data class CreateAudioAnalyticsEventRequest(
    val uniqueViewId: String,
    val userId: String,
    val audioId: String,
    val playerId: String?,
    val podcastId: String?,
    val eventType: String = "audioevent",
    val event: String?,
    val adId: String?,
    val countryCode: String?,
    val browser: String?,
    val operatingSystem: String?,
    val deviceType: String?,
    val duration: Double?,
    val userAgent: String?,
    val domain: String?,
    val userIp: String?,
    val url: String?,
)

data class AddAudioWatchStateRequest(
    val uniqueViewId: String,
    val userId: String,
    val audioId: String,
    val playerId: String?,
    val podcastId: String?,
    val eventType: String = "watchState",
    val muted: Boolean,
    val currentTime: Double,
    val segmentWatchTime: Int?,
    val volume: Double,
    val duration: Double?,
)

data class VideoKeyMomentsCardModelResponseModel(
    val averageViewDuration: Double,
    val averageViewDurationPercentage: Double,
)

data class AudioKeyMomentsCardModel(
    val averageListenTime: Double,
)

data class MetricsModel(val total: Double)

data class GjirafaChannelResponse(
    val publicId: String,
    val name: String,
    val title: String,
    val logo: String?,
    val streamServer: String,
    val streamKey: String,
    val playbackUrl: String,
    val liveStatus: LiveVideoStatus?,
    val healthStatus: HealthStatus,
    val inputSource: String?,
)

data class LiveVideoResponseV1(
    val id: String,
    val title: String,
    val description: String?,
    val playbackUrl: String,
    val thumbnail: String?,
    val channelPublicId: String,
    val channelName: String?,
    val channelTitle: String?,
    val liveStatus: LiveVideoStatus,
    val author: String?,
    // TODO Gjirafa does not return `Z` for timezone, we need to use LocalDateTime
    val publishDate: LocalDateTime?,
    // TODO Gjirafa does not return `Z` for timezone, we need to use LocalDateTime
    val publishEndDate: LocalDateTime?,
    val dvrEnabled: Boolean,
    val canCutAndPublish: Boolean,
    val canStopAndCut: Boolean,
    val canStopWithoutSaving: Boolean,
)

data class LiveVideoResponseV2(
    val id: String,
    val title: String,
    val description: String?,
    val playbackUrl: String,
    val thumbnail: String,
    val channelPublicId: String,
    val channelName: String?,
    val channelTitle: String?,
    val liveStatus: LiveVideoStatus,
    val author: String,
    val publishDate: Instant?,
    val publishEndDate: Instant?,
    val canCutAndPublish: Boolean,
    val canStopAndCut: Boolean,
    val startDateUTC: Instant?,
    val viewerCount: Int = 0,
)

data class LiveVideoResponseV2Minimal(
    val id: String,
    val liveStatus: LiveVideoStatus,
    val startDateUTC: Instant?,
    val viewerCount: Int = 0,
)

enum class LiveVideoStatus {
    LIVE,
    OFFLINE,
    PROCESSING,
    INTERRUPTED,
}

enum class HealthStatus {
    /** channel is sending input correctly and the input is being processed */
    HEALTHY,
    /** channel's input had dropped therefore there are interruptions */
    STARVING,
    /** user still hasn't send any input to the channel therefore the health is unkonwn */
    UNKNOWN,
}

data class MediaItemRequest(
    val publicId: String,
    val title: String,
    val author: String,
    val description: String?,
    val chapterState: Boolean,
    val customParameters: List<CustomProperty> = listOf(),
    val chapters: List<Chapter>,
)

data class CustomProperty(val key: String, val value: String)

data class DeleteAudiosRequest(val ids: List<String>)

// https://vp.gjirafa.tech/documentation/api/analytics-api/videoAnalytics/V2/metricsAndIntervalsTypes/
enum class VideoAnalyticsGraphMetrics(val value: Int) {
    EMBED(0),
    UNIQUE_VIEWS(1),
    VIEWS(2),
    WATCH_TIMES(3),
    COMPLETES(4),
    AVERAGE_VIEW_DURATION(5),
}

// https://vp.gjirafa.tech/documentation/api/analytics-api/audioAnalytics/metricsAndIntervalTypes/
enum class AudioAnalyticsGraphMetrics(val value: Int) {
    EMBED(0),
    AVERAGE_LISTEN_TIME(1),
    LISTENERS(2),
    UNIQUE_LISTENERS(3),
    LISTEN_TIME(4),
    LISTEN_PER_EPISODE(5),
    CONSUMPTION_RATE(6),
    COMPLETES(7),
}

// (same for audios/videos)
// https://vp.gjirafa.tech/documentation/api/analytics-api/audioAnalytics/metricsAndIntervalTypes/
enum class GjirafaIntervals(val value: Int) {
    LAST_24_HOURS(0),
    LAST_7_DAYS(1),
    LAST_MONTH(2),
    LAST_2_MONTHS(3),
    LAST_3_MONTHS(4),
    CUSTOM(5),
    LAST_30_MINUTES(6),
    LAST_3_HOURS(7),
    LAST_6_HOURS(8),
}

enum class GjirafaStreamType(val value: Int) {
    STANDARD(1),
    SLOW_TV(2),
}

enum class GjirafaLatencyType(val value: Int) {
    NORMAL(1),
    LOW(2),
    // "rewinding" is impossible with ULTRA_LOW
    ULTRA_LOW(3),
}

enum class GjirafaInputType(val value: Int) {
    PULL(1),
    PUSH(2),
}

data class CutLivestreamRequest(
    val title: String,
    val fromMilisecond: Int,
    val toMilisecond: Int,
) {
    /**
     * Visibility option has no effect for us, we use `2` as private.
     * @see: https://vp.gjirafa.tech/documentation/api/manage-api/visibilityOptions/getVisibilityOptionsAsList/
     * 1: public, 2: private, 3: password protected
     */
    val videoVisibilityOptionId: Int = 2
}

data class StopAndCutRequest(
    val title: String?,
    val fromMilisecond: Int?,
    val toMilisecond: Int?,
    val hasDrm: Boolean = false,
) {
    /**
     * Visibility option has no effect for us, we use `2` as private.
     * @see: https://vp.gjirafa.tech/documentation/api/manage-api/visibilityOptions/getVisibilityOptionsAsList/
     * 1: public, 2: private, 3: password protected
     */
    val videoVisibilityOptionId: Int = 2
}

data class CutLivestreamResponse(
    val videoPublicId: String,
    val name: String?,
    val originalFile: String?,
)

data class PostLiveVideoRequest(
    val title: String,
    val author: String,
    val description: String?,
    val channelId: String?,
    val dvrEnabled: Boolean,
    /**
     * @see: https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V1/getRewndInputTypes
     * 1: rewind from the start of video, 2: rewind with specific seconds
     */
    val rewindTypeId: Int,
    val rewindDuration: Int,
    val publishDate: Instant?,
    val publishEndDate: Instant?,
)

data class PutLiveVideoRequest(
    val title: String,
    val author: String,
    val description: String,
    val publishDate: Instant?,
    val publishEndDate: Instant?,
)

data class PostLiveVideoThumbnail(
    val id: String,
    val path: String,
)

data class LiveViewerResponse(val total: Int)

fun LiveVideoStatus.toDomainStatus(): ModelLiveVideoStatus =
    when (this) {
        LiveVideoStatus.LIVE -> ModelLiveVideoStatus.LIVE
        LiveVideoStatus.PROCESSING -> ModelLiveVideoStatus.PROCESSING
        LiveVideoStatus.INTERRUPTED -> ModelLiveVideoStatus.INTERRUPTED
        LiveVideoStatus.OFFLINE -> ModelLiveVideoStatus.OFFLINE
    }
