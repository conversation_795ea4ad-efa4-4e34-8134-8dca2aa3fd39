package hero.gcloud

import kotlin.reflect.KClass
import kotlin.reflect.KProperty1

data class DocumentRoot<R : Any>(private val type: KClass<R>) {
    fun <V> path(property: KProperty1<R, V>) = DocumentPathBuilder(this, property)
}

inline fun <reified R : Any> root() = DocumentRoot(R::class)

inline fun <reified R : Any, T> root(property: KProperty1<R, T>) = root<R>().path(property)

data class DocumentPathBuilder<R : Any, T, V> internal constructor(
    val root: DocumentRoot<R>,
    val head: KProperty1<T, V>?,
    val path: List<String> = head?.name?.let { listOf(it) } ?: emptyList(),
) {
    fun <K> path(property: KProperty1<out V, K>): DocumentPathBuilder<R, out V, K> =
        DocumentPathBuilder(this.root, property, this.path + property.name)

    fun build() = DocumentPath<R, V>(root, (this.path).joinToString(separator = "."))
}

@PublishedApi
internal fun <R : Any, T, V> documentPathBuilder(
    root: DocumentRoot<R>,
    head: KProperty1<T, V>?,
    path: List<String>,
) = DocumentPathBuilder(root, head, path)

inline fun <reified R : Any, T, K, V, MAP : Map<K, V>> DocumentPathBuilder<R, T, MAP>.entry(key: K) =
    documentPathBuilder<R, K, V>(this.root, null, this.path + key.toString())

data class DocumentPath<R : Any, T> internal constructor(val root: DocumentRoot<R>, val path: String)
