/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Chapter } from './Chapter';
import type { PostAssetDto } from './PostAssetDto';
import type { PostCounts } from './PostCounts';
import type { PostState } from './PostState';
export type PostDtoAttributes = {
    state?: PostState;
    text?: string | null;
    textHtml?: string | null;
    textDelta?: string | null;
    fullAsset?: boolean | null;
    excludeFromRss?: boolean | null;
    pinnedAt?: string | null;
    assets: Array<PostAssetDto>;
    counts?: PostCounts;
    publishedAt?: string | null;
    price?: number | null;
    assetsCount?: number | null;
    chapters: Array<Chapter>;
    isAgeRestricted: boolean;
    isSponsored: boolean;
};

